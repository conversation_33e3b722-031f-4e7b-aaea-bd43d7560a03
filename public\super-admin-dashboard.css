/* Super Admin Dashboard Styles */
body {
    background-image: url('BG.png');
    background-size: cover;
    font-family: 'Montserrat', sans-serif;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-color: rgba(255, 255, 255, 0.8);
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

/* Loading Overlay with <PERSON>tie Animation - Hidden by default */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    display: none; /* Hidden by default - skeleton loaders handle loading states */
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.loading-overlay.show {
    opacity: 1;
}

.loading-animation {
    width: 120px;
    height: 120px;
    margin-bottom: 1.5rem;
}

.loading-text {
    color: white;
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.9;
}

/* Skeleton Loading Animations */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.skeleton-card,
.skeleton-text,
.skeleton-icon,
.skeleton-row,
.skeleton-chart,
.skeleton-analytics-card,
.skeleton-stat-card {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 0.5rem;
}

.skeleton-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
}

.skeleton-text {
    height: 1rem;
    border-radius: 0.25rem;
}

.skeleton-text.large {
    height: 1.5rem;
    width: 60%;
}

.skeleton-text.medium {
    height: 1rem;
    width: 80%;
}

.skeleton-text.small {
    height: 0.75rem;
    width: 40%;
}

.skeleton-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    flex-shrink: 0;
}

/* Section Skeleton Loaders */
.section-skeleton {
    padding: 1.5rem;
    animation-delay: var(--delay, 0s);
}

.skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.skeleton-controls {
    display: flex;
    gap: 1rem;
}

.skeleton-table {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.skeleton-row {
    height: 3rem;
    border-radius: 0.5rem;
    animation-delay: var(--delay);
}

.skeleton-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.skeleton-chart {
    height: 300px;
    border-radius: 0.75rem;
}

.skeleton-analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.skeleton-analytics-card {
    height: 250px;
    border-radius: 0.75rem;
}

.skeleton-security-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.skeleton-stat-card {
    height: 100px;
    border-radius: 0.5rem;
}

/* Fade-in animations for content */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeInUp 0.6s ease-out forwards;
}

.fade-in-delayed {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
    animation-delay: var(--delay, 0s);
}

/* Fallback spinner for when Lottie fails */
.fallback-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #1547bb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Smooth transitions for all interactive elements */
* {
    transition-property: transform, box-shadow, background-color, border-color, opacity;
    transition-duration: 0.2s;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover effects */
.metric-card,
.chart-card,
.analytics-card,
.summary-card,
.stat-card {
    cursor: pointer;
}

.metric-card:hover,
.chart-card:hover,
.analytics-card:hover,
.summary-card:hover,
.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Improved button animations */
button {
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

button:active::before {
    width: 300px;
    height: 300px;
}

/* Enhanced loading states */
.loading-state {
    position: relative;
    overflow: hidden;
}

.loading-state::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading-sweep 1.5s infinite;
}

@keyframes loading-sweep {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Header - Compact Design */
.header {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: all 0.3s ease;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0.75rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo {
    height: 28px;
}

.header-title h1 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.header-title p {
    font-size: 0.8rem;
    color: #6b7280;
    margin: 0;
    opacity: 0.8;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.admin-badge {
    background: linear-gradient(135deg, #dc2626, #991b1b);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.admin-email {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #ef4444;
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

.logout-btn:hover {
    background-color: #dc2626;
}

/* Navigation - Compact Design */
.navigation {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 70px;
    z-index: 90;
    transition: all 0.3s ease;
}

.nav-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    gap: 0.25rem;
    overflow-x: auto;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    padding: 0.75rem 1.25rem;
    font-size: 0.8rem;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 2px solid transparent;
    white-space: nowrap;
    border-radius: 0.5rem 0.5rem 0 0;
}

.nav-item:hover {
    color: #1547bb;
    background-color: rgba(21, 71, 187, 0.05);
    transform: translateY(-1px);
}

.nav-item.active {
    color: #1547bb;
    border-bottom-color: #1547bb;
    background-color: rgba(21, 71, 187, 0.08);
    font-weight: 600;
}

/* Main Content - Compact Design */
.main-content {
    padding: 1rem 0;
    min-height: calc(100vh - 140px);
}

.content-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Dashboard Sections with Smooth Transitions */
.dashboard-section {
    display: none;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-section.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.section-content {
    animation: fadeInUp 0.6s ease-out;
}

/* Compact Section Headers */
.section-header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.section-header.compact {
    margin-bottom: 1.5rem;
    border-bottom: none;
}

.section-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-controls input {
    padding: 0.4rem 0.8rem;
    border: 1px solid #d1d5db;
    border-radius: 0.4rem;
    font-size: 0.8rem;
    background-color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.section-controls input:focus {
    outline: none;
    border-color: #1547bb;
    box-shadow: 0 0 0 2px rgba(21, 71, 187, 0.1);
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    background-color: #1547bb;
    color: white;
    border: none;
    border-radius: 0.4rem;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.refresh-btn:hover {
    background-color: #0f3699;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(21, 71, 187, 0.2);
}

/* Modern Overview Section */
.overview-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 24px;
}

.overview-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Modern Metric Cards */
.overview-metrics {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    margin-bottom: 32px;
}

.overview-metric-card {
    background: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 24px;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.overview-metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.overview-metric-title {
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #6B7280;
    font-weight: 500;
    margin-bottom: 8px;
}

.overview-metric-value {
    font-size: 32px;
    font-weight: 700;
    color: #111827;
    line-height: 1;
    margin-bottom: 4px;
}

.overview-metric-change {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 10px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 2px;
}

.overview-metric-change.positive {
    background-color: #10B981;
    color: white;
}

.overview-metric-change.negative {
    background-color: #EF4444;
    color: white;
}

.overview-metric-change::before {
    content: "▲";
    font-size: 8px;
}

.overview-metric-change.negative::before {
    content: "▼";
}

.overview-metric-no-data {
    font-size: 14px;
    color: #9CA3AF;
    font-style: italic;
}

/* Modern Charts Section */
.overview-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-top: 32px;
}

.overview-chart-card {
    background: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.overview-chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.overview-chart-card .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.overview-chart-card .chart-header h3 {
    font-size: 16px;
    color: #111827;
    margin: 0;
    font-weight: 600;
}

.chart-select {
    font-size: 12px;
    padding: 6px 12px;
    border: 1px solid #D1D5DB;
    border-radius: 6px;
    background: white;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-select:focus {
    outline: none;
    border-color: #1547bb;
    box-shadow: 0 0 0 2px rgba(21, 71, 187, 0.1);
}

.overview-chart-card .chart-container {
    height: 300px;
    width: 100%;
}

/* Skeleton loaders for overview */
.overview-metric-card.skeleton-card .skeleton-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.skeleton-text.tiny {
    height: 8px;
    width: 30%;
    border-radius: 4px;
}

/* Chart Skeleton Loaders */
.chart-skeleton {
    width: 100%;
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.skeleton-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.skeleton-chart-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.skeleton-chart-area {
    flex: 1;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 0.5rem;
    min-height: 200px;
}

.skeleton-chart-legend {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 0.5rem;
}

.skeleton-chart-legend .skeleton-text.tiny {
    width: 60px;
    height: 12px;
}

/* Charts Grid - Compact Design */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.chart-card {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 0.6rem;
    padding: 1.2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.8rem;
}

.chart-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.chart-controls select {
    padding: 0.3rem 0.6rem;
    border: 1px solid #d1d5db;
    border-radius: 0.3rem;
    font-size: 0.8rem;
    background-color: white;
    transition: all 0.2s ease;
}

.chart-controls select:focus {
    outline: none;
    border-color: #1547bb;
    box-shadow: 0 0 0 2px rgba(21, 71, 187, 0.1);
}

.chart-container {
    position: relative;
    height: 260px;
    opacity: 0;
    animation: fadeInChart 0.8s ease-out 0.3s forwards;
}

@keyframes fadeInChart {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Table Styles */
.table-container {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.table-loading,
.analytics-loading {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
    font-size: 1.125rem;
}

/* Table Styles - Compact Design */
.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.table-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.search-input {
    padding: 0.4rem 0.8rem;
    border: 1px solid #d1d5db;
    border-radius: 0.4rem;
    font-size: 0.8rem;
    background-color: white;
    min-width: 180px;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #1547bb;
    box-shadow: 0 0 0 2px rgba(21, 71, 187, 0.1);
    transform: scale(1.02);
}

.filter-select {
    padding: 0.4rem 0.8rem;
    border: 1px solid #d1d5db;
    border-radius: 0.4rem;
    font-size: 0.8rem;
    background-color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #1547bb;
    box-shadow: 0 0 0 2px rgba(21, 71, 187, 0.1);
}

.export-btn {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    background-color: #10b981;
    color: white;
    border: none;
    border-radius: 0.4rem;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.export-btn:hover {
    background-color: #059669;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.table-wrapper {
    overflow-x: auto;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    font-size: 0.875rem;
}

.data-table th {
    background-color: #f9fafb;
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    white-space: nowrap;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: top;
}

.data-table tr:hover {
    background-color: #f9fafb;
}

.admin-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.admin-name {
    font-weight: 600;
    color: #1f2937;
}

.admin-id {
    font-size: 0.75rem;
    color: #6b7280;
    font-family: monospace;
}

.company-name {
    font-weight: 500;
    color: #1f2937;
}

.admin-email {
    color: #1547bb;
    font-weight: 500;
}

.credits-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.credits-count {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
}

.credits-label {
    font-size: 0.75rem;
    color: #6b7280;
}

.subscription-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.subscription-badge.paid {
    background-color: #dcfce7;
    color: #166534;
}

.subscription-badge.trial {
    background-color: #fef3c7;
    color: #92400e;
}

.subscription-badge.free {
    background-color: #f3f4f6;
    color: #374151;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.paid {
    background-color: #dcfce7;
    color: #166534;
}

.status-badge.trial {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-badge.active {
    background-color: #dcfce7;
    color: #166534;
}

.status-badge.expired {
    background-color: #fef2f2;
    color: #dc2626;
}

.lead-source {
    padding: 0.25rem 0.5rem;
    background-color: #f3f4f6;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: #374151;
}

.date-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.date-primary {
    font-weight: 500;
    color: #1f2937;
}

.date-secondary {
    font-size: 0.75rem;
    color: #6b7280;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

.view-btn {
    background-color: #dbeafe;
    color: #1e40af;
}

.view-btn:hover {
    background-color: #bfdbfe;
}

.table-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.table-info {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Enhanced Responsive Design */
@media (max-width: 640px) {
    /* Overview responsive - Mobile */
    .overview-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .overview-controls input,
    .overview-controls button {
        width: 100%;
    }

    .overview-metrics {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 24px;
    }

    .overview-metric-card {
        padding: 16px;
    }

    .overview-metric-value {
        font-size: 24px;
    }

    .overview-metric-change {
        top: 12px;
        right: 12px;
        font-size: 9px;
        padding: 3px 6px;
    }

    .overview-charts {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .overview-chart-card {
        padding: 16px;
    }

    .overview-chart-card .chart-container {
        height: 250px;
    }
}

@media (min-width: 641px) and (max-width: 1023px) {
    /* Overview responsive - Tablet */
    .overview-metrics {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .overview-charts {
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .overview-chart-card .chart-container {
        height: 280px;
    }
}

@media (min-width: 1024px) {
    /* Overview responsive - Desktop */
    .overview-metrics {
        grid-template-columns: repeat(4, 1fr);
        gap: 24px;
    }

    .overview-charts {
        grid-template-columns: 1fr 1fr;
        gap: 24px;
    }

    .overview-chart-card .chart-container {
        height: 300px;
    }
}

/* Legacy responsive design for other sections */
@media (max-width: 768px) {
    /* Header adjustments */
    .header-content {
        padding: 0.5rem 1rem;
        flex-direction: column;
        gap: 0.75rem;
    }

    .header-title h1 {
        font-size: 1.1rem;
    }

    .header-title p {
        font-size: 0.75rem;
    }

    /* Navigation adjustments */
    .navigation {
        top: 60px;
    }

    .nav-content {
        padding: 0 1rem;
        gap: 0.1rem;
    }

    .nav-item {
        padding: 0.6rem 1rem;
        font-size: 0.75rem;
    }

    /* Content adjustments */
    .main-content {
        padding: 0.75rem 0;
        min-height: calc(100vh - 120px);
    }

    .content-container {
        padding: 0 1rem;
    }

    .section-header.compact {
        margin-bottom: 1rem;
    }

    .section-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .section-controls input,
    .section-controls select {
        width: 100%;
    }

    /* Charts adjustments for other sections */
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .chart-container {
        height: 220px;
    }

    .chart-header h3 {
        font-size: 0.9rem;
    }

    /* Table adjustments */
    .table-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .table-header h3 {
        font-size: 0.9rem;
    }

    .table-controls {
        justify-content: stretch;
        flex-direction: column;
        gap: 0.5rem;
    }

    .search-input,
    .filter-select {
        min-width: auto;
        width: 100%;
    }

    .data-table {
        font-size: 0.7rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.4rem;
    }

    /* Loading overlay adjustments */
    .loading-animation {
        width: 80px;
        height: 80px;
        margin-bottom: 1rem;
    }

    .loading-text {
        font-size: 0.9rem;
    }

    /* Modal adjustments */
    .modal-content {
        width: 95%;
        max-height: 85vh;
        margin: 0 auto;
    }

    .modal-body {
        padding: 1rem;
    }

    .admin-details-grid,
    .company-details-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Button adjustments */
    .refresh-btn,
    .export-btn,
    .clear-btn {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }

    /* Skeleton adjustments */
    .skeleton-charts {
        grid-template-columns: 1fr;
    }

    .skeleton-analytics-grid {
        grid-template-columns: 1fr;
    }

    .skeleton-security-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .metric-card {
        padding: 0.75rem;
    }

    .metric-value {
        font-size: 1.2rem;
    }

    .chart-container {
        height: 200px;
    }

    .skeleton-security-stats {
        grid-template-columns: 1fr;
    }

    .nav-item {
        padding: 0.5rem 0.75rem;
        font-size: 0.7rem;
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-content {
    background-color: white;
    border-radius: 0.75rem;
    padding: 0;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.95);
    transition: transform 0.3s ease;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.modal-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: 0.375rem;
    background-color: #f3f4f6;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

.modal-close:hover {
    background-color: #e5e7eb;
    color: #374151;
}

.modal-body {
    padding: 1.5rem;
}

.admin-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.detail-section {
    background-color: #f9fafb;
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.detail-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item label {
    font-weight: 500;
    color: #6b7280;
    font-size: 0.875rem;
}

.detail-item span {
    font-weight: 500;
    color: #1f2937;
    text-align: right;
}

.credits-highlight {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1547bb;
}

.text-green-600 {
    color: #059669;
}

.text-gray-600 {
    color: #6b7280;
}

/* Stat Cards */
.company-stats,
.user-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1547bb;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* Additional Status Badges */
.status-badge.inactive {
    background-color: #f3f4f6;
    color: #6b7280;
}

.status-badge.large {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-badge.completed {
    background-color: #dcfce7;
    color: #166534;
}

.status-badge.started {
    background-color: #fef3c7;
    color: #92400e;
}

.status-badge.pending {
    background-color: #e0e7ff;
    color: #3730a3;
}

.status-badge.unknown {
    background-color: #f3f4f6;
    color: #6b7280;
}

/* User Count Info */
.user-count-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.user-count {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
}

.user-label {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Company and User Info */
.company-info,
.user-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.company-name-display,
.user-name {
    font-weight: 600;
    color: #1f2937;
}

.user-email {
    color: #1547bb;
    font-weight: 500;
}

.user-role {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Modal Extensions */
.company-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.full-width {
    grid-column: 1 / -1;
}

.users-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 300px;
    overflow-y: auto;
}

.user-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
}

.user-item .user-info {
    flex: 1;
}

.user-item .user-name {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.user-item .user-email {
    font-size: 0.875rem;
    color: #6b7280;
}

.user-status {
    flex-shrink: 0;
}

/* Assessment Analytics */
.assessment-analytics {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.analytics-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.analytics-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.assessment-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.assessment-charts {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.chart-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.chart-row .full-width {
    grid-column: 1 / -1;
}

.chart-card h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.company-assessment-breakdown {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.breakdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.breakdown-header h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.breakdown-table {
    overflow-x: auto;
}

.assessment-count {
    font-weight: 600;
    color: #1f2937;
    text-align: center;
}

.completion-rate {
    font-weight: 600;
    text-align: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.completion-rate.good {
    background-color: #dcfce7;
    color: #166534;
}

.completion-rate.fair {
    background-color: #fef3c7;
    color: #92400e;
}

.completion-rate.poor {
    background-color: #fef2f2;
    color: #dc2626;
}

/* Responsive adjustments for assessment analytics */
@media (max-width: 768px) {
    .chart-row {
        grid-template-columns: 1fr;
    }

    .assessment-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .analytics-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
}

/* Advanced Analytics */
.advanced-analytics {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.analytics-card {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.analytics-card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.card-header h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.card-header select {
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background-color: white;
}

.growth-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-container.large {
    height: 400px;
}

.card-stats {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-item .stat-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.stat-item .stat-value {
    font-weight: 600;
    color: #1f2937;
}

.lead-source-stats {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.lead-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background-color: #f9fafb;
    border-radius: 0.375rem;
}

.lead-source-name {
    font-weight: 500;
    color: #1f2937;
}

.lead-count {
    font-size: 0.875rem;
    color: #6b7280;
}

.subscription-breakdown {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
}

.breakdown-label {
    font-weight: 500;
    color: #1f2937;
}

.breakdown-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1547bb;
}

.breakdown-percentage {
    font-size: 0.875rem;
    color: #6b7280;
}

.heatmap-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    border: 2px dashed #d1d5db;
}

.heatmap-placeholder {
    text-align: center;
    color: #6b7280;
}

.heatmap-placeholder p {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.info-tooltip {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.25rem;
    height: 1.25rem;
    background-color: #e5e7eb;
    color: #6b7280;
    border-radius: 50%;
    font-size: 0.75rem;
    cursor: help;
}

/* Export Section */
.export-section {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 0.75rem;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.export-header {
    margin-bottom: 2rem;
}

.export-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.export-header p {
    color: #6b7280;
    margin: 0;
}

.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.export-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background-color: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    text-align: left;
}

.export-option:hover {
    border-color: #1547bb;
    background-color: rgba(21, 71, 187, 0.05);
}

.export-option svg {
    flex-shrink: 0;
    color: #1547bb;
}

.option-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.option-description {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Responsive adjustments for advanced analytics */
@media (max-width: 768px) {
    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .growth-controls {
        flex-direction: column;
    }

    .export-options {
        grid-template-columns: 1fr;
    }

    .breakdown-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* Security Audit Styles */
.security-audit {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.security-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.security-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.security-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.clear-btn {
    background-color: #ef4444;
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

.clear-btn:hover {
    background-color: #dc2626;
}

.security-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1547bb;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.summary-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.audit-log-container {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.log-table-wrapper {
    overflow-x: auto;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
}

.audit-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    font-size: 0.875rem;
}

.audit-table th {
    background-color: #f9fafb;
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    white-space: nowrap;
}

.audit-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: top;
}

.audit-table tr:hover {
    background-color: #f9fafb;
}

.timestamp {
    font-family: monospace;
    font-size: 0.8rem;
    color: #6b7280;
}

.log-type {
    font-weight: 500;
}

.action {
    font-weight: 500;
    color: #1f2937;
}

.details {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #6b7280;
    cursor: help;
}

.status-success {
    background-color: #dcfce7;
    color: #166534;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-failed {
    background-color: #fef2f2;
    color: #dc2626;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-info {
    background-color: #dbeafe;
    color: #1e40af;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.session-id {
    font-family: monospace;
    font-size: 0.75rem;
    color: #6b7280;
}

.log-footer {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.log-info {
    font-size: 0.875rem;
    color: #6b7280;
}

.security-recommendations {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.security-recommendations h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
}

.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    border-left: 4px solid #1547bb;
}

.recommendation-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.recommendation-text {
    flex: 1;
    font-size: 0.875rem;
    line-height: 1.5;
}

.recommendation-text strong {
    color: #1f2937;
}

/* Responsive adjustments for security section */
@media (max-width: 768px) {
    .security-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .security-controls {
        justify-content: stretch;
        flex-direction: column;
    }

    .security-summary {
        grid-template-columns: repeat(2, 1fr);
    }

    .audit-table {
        font-size: 0.75rem;
    }

    .audit-table th,
    .audit-table td {
        padding: 0.5rem;
    }
}
