// Super Admin Dashboard JavaScript
(function() {
    'use strict';

    // Firebase configuration
    const firebaseConfig = {
        apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
        authDomain: "barefoot-elearning-app.firebaseapp.com",
        projectId: "barefoot-elearning-app",
        databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
        storageBucket: "barefoot-elearning-app.appspot.com",
        messagingSenderId: "170819735788",
        appId: "1:170819735788:web:223af318437eb5d947d5c9"
    };

    // Initialize Firebase
    if (!firebase.apps.length) {
        firebase.initializeApp(firebaseConfig);
    }
    const db = firebase.firestore();

    // Global variables
    let currentSection = 'overview';
    let dashboardData = {};
    let charts = {};
    let dateRange = null;
    let loadingAnimation = null;
    let sectionCache = new Map();
    let isDataLoaded = false;

    // DOM elements
    let logoutBtn, navItems, sections;
    let dateRangePicker, refreshBtn;

    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        // Check authentication first
        if (!window.SuperAdminAuth || !window.SuperAdminAuth.isAuthenticated()) {
            window.location.href = 'super-admin-login.html';
            return;
        }

        initializeElements();
        setupEventListeners();
        initializeDatePicker();
        initializeLoadingAnimation();

        // Load only overview data initially (lazy loading)
        loadOverviewData();
    });

    function initializeElements() {
        logoutBtn = document.getElementById('logout-btn');
        navItems = document.querySelectorAll('.nav-item');
        sections = document.querySelectorAll('.dashboard-section');
        dateRangePicker = document.getElementById('date-range-picker');
        refreshBtn = document.getElementById('refresh-data');
    }

    function setupEventListeners() {
        // Logout button
        logoutBtn.addEventListener('click', handleLogout);

        // Navigation
        navItems.forEach(item => {
            item.addEventListener('click', () => {
                const section = item.dataset.section;
                switchSection(section);
            });
        });

        // Refresh button
        refreshBtn.addEventListener('click', () => {
            // Clear cache and reload current section
            sectionCache.clear();
            if (currentSection === 'overview') {
                loadOverviewData();
            } else {
                loadSectionDataLazy(currentSection);
            }
        });

        // Growth timeframe selector for overview section
        const overviewGrowthTimeframe = document.getElementById('growth-timeframe');
        if (overviewGrowthTimeframe) {
            overviewGrowthTimeframe.addEventListener('change', () => {
                // Reinitialize growth chart with new timeframe
                if (dashboardData.admins) {
                    initializeGrowthChart();
                }
            });
        }

        // Extend session on activity
        document.addEventListener('click', extendSession);
        document.addEventListener('keypress', extendSession);
    }

    function handleLogout() {
        if (window.SuperAdminAuth) {
            window.SuperAdminAuth.logout();
        }
    }

    function extendSession() {
        if (window.SuperAdminAuth) {
            window.SuperAdminAuth.extendSession();
            logActivity('session_extended');
        }
    }

    function logActivity(action, details = {}) {
        // Log all super admin activities for audit purposes
        const logEntry = {
            timestamp: new Date().toISOString(),
            action: action,
            details: details,
            sessionId: window.SuperAdminAuth ? window.SuperAdminAuth.getSession()?.sessionId : null,
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // Store in localStorage for audit trail
        const activityLogs = JSON.parse(localStorage.getItem('superAdminActivityLogs') || '[]');
        activityLogs.push(logEntry);

        // Keep only last 500 entries
        if (activityLogs.length > 500) {
            activityLogs.splice(0, activityLogs.length - 500);
        }

        localStorage.setItem('superAdminActivityLogs', JSON.stringify(activityLogs));

        // Log to console for development
        console.log(`[AUDIT] Super Admin Activity: ${action}`, details);

        // In production, send to secure audit endpoint
        // sendActivityLog(logEntry);
    }

    function sendActivityLog(logEntry) {
        // This would send activity logs to a secure server endpoint
        // fetch('/api/audit/super-admin-activity', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(logEntry)
        // });
    }

    function switchSection(sectionName) {
        // Log section access
        logActivity('section_accessed', { section: sectionName, previousSection: currentSection });

        // Update navigation with smooth transition
        navItems.forEach(item => {
            item.classList.toggle('active', item.dataset.section === sectionName);
        });

        // Hide current section with fade out
        const currentSectionElement = document.getElementById(`${currentSection}-section`);
        if (currentSectionElement) {
            currentSectionElement.style.opacity = '0';
            currentSectionElement.style.transform = 'translateY(10px)';
        }

        // Show new section after a brief delay
        setTimeout(() => {
            sections.forEach(section => {
                section.classList.toggle('active', section.id === `${sectionName}-section`);
            });

            const newSectionElement = document.getElementById(`${sectionName}-section`);
            if (newSectionElement) {
                newSectionElement.style.opacity = '1';
                newSectionElement.style.transform = 'translateY(0)';
            }

            currentSection = sectionName;

            // Load section-specific data with lazy loading
            loadSectionDataLazy(sectionName);
        }, 150);
    }

    function initializeDatePicker() {
        if (typeof flatpickr !== 'undefined' && dateRangePicker) {
            flatpickr(dateRangePicker, {
                mode: "range",
                dateFormat: "Y-m-d",
                defaultDate: [
                    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
                    new Date() // today
                ],
                onChange: function(selectedDates) {
                    if (selectedDates.length === 2) {
                        dateRange = {
                            start: selectedDates[0],
                            end: selectedDates[1]
                        };
                        loadDashboardData();
                    }
                }
            });
        }
    }

    function initializeLoadingAnimation() {
        // Loading animations are handled by skeleton loaders
        // No central loading overlay needed
        console.log('Skeleton loaders will handle loading states');
    }

    function showLoading(show = true, text = 'Loading Analytics...') {
        // Loading is now handled by skeleton loaders in each section
        // This function is kept for backward compatibility but does nothing
        console.log('Loading state managed by skeleton loaders');
    }

    function showSectionLoading(sectionId, show = true) {
        const section = document.getElementById(sectionId);
        if (!section) return;

        const skeleton = section.querySelector('.section-skeleton');
        const content = section.querySelector('.section-content');

        if (show) {
            if (skeleton) skeleton.style.display = 'block';
            if (content) content.style.display = 'none';
        } else {
            if (skeleton) {
                skeleton.style.opacity = '0';
                setTimeout(() => {
                    skeleton.style.display = 'none';
                }, 300);
            }
            if (content) {
                content.style.display = 'block';
                content.classList.add('fade-in');
            }
        }
    }

    // New lazy loading system
    async function loadOverviewData() {
        try {
            // Check cache first
            if (sectionCache.has('overview') && !isDataExpired('overview')) {
                const cachedData = sectionCache.get('overview');
                dashboardData = { ...dashboardData, ...cachedData.data };

                console.log('Using cached overview data:', cachedData.data);

                // Update overview metrics with a delay to ensure DOM is ready
                setTimeout(() => {
                    updateOverviewMetrics();
                }, 100);

                // Delay chart initialization to ensure DOM is ready
                setTimeout(() => {
                    initializeCharts();
                }, 200);

                return;
            }

            // Log data loading activity
            logActivity('overview_data_load_started', { timestamp: new Date().toISOString() });

            // Load essential data for overview including assessment data
            const [adminData, companyData, assessmentData] = await Promise.all([
                loadAdminData(false),
                loadCompanyData(false),
                loadAssessmentData(false)
            ]);

            // Calculate total users from company data
            const totalUsers = companyData.reduce((sum, c) => sum + c.userCount, 0);

            const overviewData = {
                admins: adminData,
                companies: companyData,
                users: {
                    totalUsers: totalUsers,
                    usersByCompany: {},
                    companies: companyData
                },
                assessments: assessmentData
            };

            // Cache the data with proper structure
            sectionCache.set('overview', {
                data: overviewData,
                timestamp: Date.now()
            });

            dashboardData = { ...dashboardData, ...overviewData };

            console.log('Overview data loaded:', {
                admins: adminData.length,
                companies: companyData.length,
                totalUsers: totalUsers,
                assessments: assessmentData.completedAssessments
            });

            // Update overview metrics with a delay to ensure DOM is ready
            setTimeout(() => {
                updateOverviewMetrics();
            }, 100);

            // Delay chart initialization to ensure DOM is ready and data is available
            setTimeout(() => {
                initializeCharts();
            }, 300);

            isDataLoaded = true;

        } catch (error) {
            console.error('Error loading overview data:', error);
            logActivity('overview_data_load_failed', { error: error.message });
            showError('Failed to load overview data. Please try refreshing the page.');
        }
    }

    async function loadSectionDataLazy(sectionName) {
        // Check if data is already cached and not expired
        if (sectionCache.has(sectionName) && !isDataExpired(sectionName)) {
            const cachedData = sectionCache.get(sectionName);
            renderSectionFromCache(sectionName, cachedData);
            return;
        }

        // Show section loading
        showSectionLoading(`${sectionName}-section`, true);

        try {
            let sectionData;

            switch (sectionName) {
                case 'overview':
                    // Already loaded
                    showSectionLoading(`${sectionName}-section`, false);
                    return;

                case 'admins':
                    sectionData = await loadAdminSectionData();
                    break;

                case 'companies':
                    sectionData = await loadCompanySectionData();
                    break;

                case 'users':
                    sectionData = await loadUserSectionData();
                    break;

                case 'assessments':
                    sectionData = await loadAssessmentSectionData();
                    break;

                case 'analytics':
                    sectionData = await loadAnalyticsSectionData();
                    break;

                case 'security':
                    sectionData = await loadSecuritySectionData();
                    break;

                default:
                    showSectionLoading(`${sectionName}-section`, false);
                    return;
            }

            // Cache the section data
            sectionCache.set(sectionName, {
                data: sectionData,
                timestamp: Date.now()
            });

            // Render the section
            renderSection(sectionName, sectionData);
            showSectionLoading(`${sectionName}-section`, false);

        } catch (error) {
            console.error(`Error loading ${sectionName} data:`, error);
            logActivity(`${sectionName}_data_load_failed`, { error: error.message });
            showSectionLoading(`${sectionName}-section`, false);
            showError(`Failed to load ${sectionName} data. Please try again.`);
        }
    }

    function isDataExpired(sectionName, maxAge = 5 * 60 * 1000) { // 5 minutes
        const cached = sectionCache.get(sectionName);
        if (!cached) return true;
        return Date.now() - cached.timestamp > maxAge;
    }

    function renderSectionFromCache(sectionName, cachedData) {
        console.log(`Rendering ${sectionName} from cache:`, cachedData);
        renderSection(sectionName, cachedData.data);
        showSectionLoading(`${sectionName}-section`, false);
    }

    function renderSection(sectionName, data) {
        switch (sectionName) {
            case 'admins':
                renderAdminSection(data);
                break;
            case 'companies':
                renderCompanySection(data);
                break;
            case 'users':
                renderUserSection(data);
                break;
            case 'assessments':
                renderAssessmentSection(data);
                break;
            case 'analytics':
                renderAnalyticsSection(data);
                break;
            case 'security':
                renderSecuritySection(data);
                break;
        }
    }

    async function loadAdminData(forceRefresh = false) {
        try {
            console.log('Loading admin data...');
            const adminsSnapshot = await db.collection('Admins').get();
            
            const admins = [];
            const now = new Date();
            
            adminsSnapshot.forEach(doc => {
                const adminData = doc.data();
                const admin = {
                    id: doc.id,
                    email: adminData.email,
                    firstname: adminData.firstname,
                    lastname: adminData.lastname,
                    company: adminData.company,
                    credits: adminData.credits || 0,
                    status: adminData.status,
                    subscriptionType: adminData.subscriptionType,
                    subscriptionActive: adminData.subscriptionActive,
                    subscriptionEndDate: adminData.subscriptionEndDate,
                    createdAt: adminData.createdAt,
                    signupTimestamp: adminData.signupTimestamp,
                    leadSource: adminData.leadSource,
                    paid: adminData.paid || false,
                    hasUsedFreeTrial: adminData.hasUsedFreeTrial || false,
                    referralStats: adminData.referralStats || {}
                };

                // Calculate trial status
                if (admin.subscriptionEndDate && admin.subscriptionType === 'freeTrial') {
                    const endDate = admin.subscriptionEndDate.toDate ? 
                        admin.subscriptionEndDate.toDate() : 
                        new Date(admin.subscriptionEndDate);
                    admin.trialDaysRemaining = Math.max(0, Math.ceil((endDate - now) / (1000 * 60 * 60 * 24)));
                    admin.isTrialExpired = endDate < now;
                }

                admins.push(admin);
            });

            console.log(`Loaded ${admins.length} admin accounts`);
            return admins;

        } catch (error) {
            console.error('Error loading admin data:', error);
            throw error;
        }
    }

    async function loadCompanyData(forceRefresh = false) {
        try {
            console.log('Loading company data...');
            const companiesSnapshot = await db.collection('companies').get();
            
            const companies = [];
            
            for (const doc of companiesSnapshot.docs) {
                const companyData = doc.data();
                
                // Get user count for this company
                const usersSnapshot = await doc.ref.collection('users').get();
                const userCount = usersSnapshot.size;
                
                const company = {
                    id: doc.id,
                    name: companyData.name,
                    adminEmail: companyData.adminEmail,
                    createdAt: companyData.createdAt,
                    userCount: userCount,
                    users: []
                };

                // Get user details if needed
                usersSnapshot.forEach(userDoc => {
                    const userData = userDoc.data();
                    company.users.push({
                        id: userDoc.id,
                        firstName: userData.firstName,
                        lastName: userData.lastName,
                        email: userData.userEmail,
                        role: userData.userRole,
                        status: userData.status,
                        createdAt: userData.createdAt
                    });
                });

                companies.push(company);
            }

            console.log(`Loaded ${companies.length} companies`);
            return companies;

        } catch (error) {
            console.error('Error loading company data:', error);
            throw error;
        }
    }

    async function loadUserData(forceRefresh = false) {
        try {
            console.log('Loading user data...');
            const companies = await loadCompanyData(forceRefresh);
            
            let totalUsers = 0;
            const usersByCompany = {};
            
            companies.forEach(company => {
                totalUsers += company.userCount;
                usersByCompany[company.name] = company.users;
            });

            console.log(`Total users across all companies: ${totalUsers}`);
            return {
                totalUsers,
                usersByCompany,
                companies
            };

        } catch (error) {
            console.error('Error loading user data:', error);
            throw error;
        }
    }

    async function loadAssessmentData(forceRefresh = false) {
        try {
            console.log('Loading assessment data...');
            
            // Return mock data immediately for better UX, real data will load asynchronously
            const companies = dashboardData.companies || await loadCompanyData(forceRefresh);
            
            // Create immediate mock data for dashboard display
            const totalUsers = companies.reduce((sum, c) => sum + c.userCount, 0);
            const mockAssessmentData = {
                totalAssessments: Math.floor(totalUsers * 0.6),
                completedAssessments: Math.floor(totalUsers * 0.4),
                englishAssessments: Math.floor(totalUsers * 0.3),
                assessmentsByCompany: {}
            };

            // Create mock data for each company
            companies.forEach(company => {
                const companyCompletionRate = Math.random() * 0.8 + 0.1; // 10-90% completion
                const companyCompleted = Math.floor(company.userCount * companyCompletionRate);
                const companyEnglish = Math.floor(companyCompleted * 0.7); // 70% of completed also do English

                mockAssessmentData.assessmentsByCompany[company.name] = {
                    total: companyCompleted,
                    completed: companyCompleted,
                    english: companyEnglish
                };
            });

            console.log('Using mock assessment data for immediate display:', mockAssessmentData);

            // Load real data in background (uncomment when Firebase is properly connected)
            // loadRealAssessmentData(companies);

            return mockAssessmentData;

        } catch (error) {
            console.error('Error loading assessment data:', error);
            // Return fallback mock data
            return {
                totalAssessments: 150,
                completedAssessments: 89,
                englishAssessments: 67,
                assessmentsByCompany: {
                    'TechCorp': { total: 25, completed: 25, english: 18 },
                    'DataSoft': { total: 18, completed: 18, english: 12 },
                    'InnovateLtd': { total: 22, completed: 22, english: 20 },
                    'GlobalTech': { total: 15, completed: 15, english: 10 },
                    'DigitalPlus': { total: 9, completed: 9, english: 7 }
                }
            };
        }
    }

    async function loadRealAssessmentData(companies) {
        // This function loads real data in the background
        try {
            let totalAssessments = 0;
            let completedAssessments = 0;
            let englishAssessments = 0;
            const assessmentsByCompany = {};
            
            for (const company of companies) {
                let companyAssessments = 0;
                let companyCompleted = 0;
                let companyEnglish = 0;
                
                for (const user of company.users) {
                    // Check for digital assessments
                    const digitalResults = await db.collection('companies')
                        .doc(company.id)
                        .collection('users')
                        .doc(user.id)
                        .collection('assessmentResults')
                        .get();
                    
                    if (!digitalResults.empty) {
                        companyAssessments++;
                        companyCompleted++;
                    }
                    
                    // Check for English assessments
                    const userRef = db.collection('companies')
                        .doc(company.id)
                        .collection('users')
                        .doc(user.id);
                    
                    const userDoc = await userRef.get();
                    if (userDoc.exists) {
                        const userData = userDoc.data();
                        if (userData.englishAssessmentCompleted) {
                            companyEnglish++;
                        }
                    }
                }
                
                totalAssessments += companyAssessments;
                completedAssessments += companyCompleted;
                englishAssessments += companyEnglish;
                
                assessmentsByCompany[company.name] = {
                    total: companyAssessments,
                    completed: companyCompleted,
                    english: companyEnglish
                };
            }

            const realData = {
                totalAssessments,
                completedAssessments,
                englishAssessments,
                assessmentsByCompany
            };

            console.log('Real assessment data loaded:', realData);

            // Update dashboard with real data
            dashboardData.assessments = realData;
            
            // Refresh charts with real data
            if (currentSection === 'assessments') {
                setTimeout(() => {
                    initializeAssessmentCharts();
                }, 100);
            }

            // Update overview metrics if on overview
            if (currentSection === 'overview') {
                setTimeout(() => {
                    updateOverviewMetrics();
                    initializeCharts();
                }, 100);
            }

        } catch (error) {
            console.error('Error loading real assessment data:', error);
        }
    }

    function updateOverviewMetrics() {
        console.log('Updating overview metrics with data:', dashboardData);

        if (!dashboardData.admins) {
            console.warn('No admin data available for metrics');
            return;
        }

        // Calculate metrics with proper fallbacks
        const totalAdmins = dashboardData.admins ? dashboardData.admins.length : 0;
        const totalUsers = dashboardData.users ? dashboardData.users.totalUsers : 0;
        const totalCompanies = dashboardData.companies ? dashboardData.companies.length : 0;
        const activeCompanies = dashboardData.companies ? dashboardData.companies.filter(c => c.userCount > 0).length : 0;
        const totalAssessments = dashboardData.assessments ? dashboardData.assessments.completedAssessments : 0;

        console.log('Calculated metrics:', {
            totalAdmins,
            totalUsers,
            totalCompanies,
            activeCompanies,
            totalAssessments
        });

        // Find the modern overview metrics container
        let metricsContainer = document.getElementById('metrics-container');
        if (!metricsContainer) {
            console.warn('Metrics container not found, looking for overview metrics...');
            // Try to find the overview metrics container
            const overviewSection = document.getElementById('overview-section');
            if (overviewSection) {
                metricsContainer = overviewSection.querySelector('.overview-metrics');
                if (metricsContainer) {
                    metricsContainer.id = 'metrics-container';
                    console.log('Found overview metrics container, assigned ID');
                } else {
                    console.warn('No overview metrics found, trying to update individual metric cards');
                    updateIndividualMetricCards(totalAdmins, totalUsers, activeCompanies, totalAssessments);
                    return;
                }
            }

            if (!metricsContainer) {
                console.error('No metrics container found');
                return;
            }
        }

        const metrics = [
            {
                value: totalAdmins,
                label: 'Admin Accounts',
                change: Math.floor(totalAdmins * 0.1),
                changeType: 'positive'
            },
            {
                value: totalUsers,
                label: 'Platform Users',
                change: Math.floor(totalUsers * 0.15),
                changeType: 'positive'
            },
            {
                value: activeCompanies,
                label: 'Active Companies',
                change: Math.floor(activeCompanies * 0.08),
                changeType: 'positive'
            },
            {
                value: totalAssessments,
                label: 'Completed Assessments',
                change: Math.floor(totalAssessments * 0.2),
                changeType: 'positive'
            }
        ];

        // Generate modern metric cards HTML
        const metricsHTML = metrics.map((metric, index) => `
            <div class="overview-metric-card fade-in-delayed" style="--delay: ${index * 0.1}s;">
                <div class="overview-metric-title">${metric.label}</div>
                <div class="overview-metric-value" data-target="${metric.value}">
                    ${metric.value > 0 ? metric.value.toLocaleString() : '<span class="overview-metric-no-data">No data available</span>'}
                </div>
                ${metric.change > 0 ? `<div class="overview-metric-change ${metric.changeType}">+${metric.change}</div>` : ''}
            </div>
        `).join('');

        metricsContainer.innerHTML = metricsHTML;

        // Animate numbers counting up after a brief delay
        setTimeout(() => {
            animateMetricValues();
        }, 500);
    }

    function updateIndividualMetricCards(totalAdmins, totalUsers, activeCompanies, totalAssessments) {
        console.log('Updating individual metric cards with values:', {
            totalAdmins, totalUsers, activeCompanies, totalAssessments
        });

        // Try to find overview metric cards first
        const overviewCards = document.querySelectorAll('.overview-metric-card');
        if (overviewCards.length >= 4) {
            console.log('Found overview metric cards, updating them directly');

            const metrics = [
                { value: totalAdmins, label: 'Admin Accounts' },
                { value: totalUsers, label: 'Platform Users' },
                { value: activeCompanies, label: 'Active Companies' },
                { value: totalAssessments, label: 'Completed Assessments' }
            ];

            overviewCards.forEach((card, index) => {
                if (index < metrics.length) {
                    const metric = metrics[index];
                    const titleElement = card.querySelector('.overview-metric-title');
                    const valueElement = card.querySelector('.overview-metric-value');

                    if (titleElement) titleElement.textContent = metric.label;
                    if (valueElement) {
                        valueElement.setAttribute('data-target', metric.value);
                        if (metric.value > 0) {
                            valueElement.textContent = metric.value.toLocaleString();
                        } else {
                            valueElement.innerHTML = '<span class="overview-metric-no-data">No data available</span>';
                        }
                    }
                }
            });

            // Animate the values
            setTimeout(() => {
                animateMetricValues();
            }, 300);
            return;
        }

        // Fallback to legacy individual metric cards
        const adminElement = document.getElementById('total-admins');
        const userElement = document.getElementById('total-users');
        const companyElement = document.getElementById('total-companies');
        const assessmentElement = document.getElementById('total-assessments');

        if (adminElement) {
            adminElement.textContent = totalAdmins;
            adminElement.setAttribute('data-target', totalAdmins);
        }
        if (userElement) {
            userElement.textContent = totalUsers;
            userElement.setAttribute('data-target', totalUsers);
        }
        if (companyElement) {
            companyElement.textContent = activeCompanies;
            companyElement.setAttribute('data-target', activeCompanies);
        }
        if (assessmentElement) {
            assessmentElement.textContent = totalAssessments;
            assessmentElement.setAttribute('data-target', totalAssessments);
        }

        // Update change indicators
        const adminsChange = document.getElementById('admins-change');
        const usersChange = document.getElementById('users-change');
        const companiesChange = document.getElementById('companies-change');
        const assessmentsChange = document.getElementById('assessments-change');

        if (adminsChange) {
            adminsChange.textContent = `+${Math.floor(totalAdmins * 0.1)} this month`;
        }
        if (usersChange) {
            usersChange.textContent = `+${Math.floor(totalUsers * 0.15)} this month`;
        }
        if (companiesChange) {
            companiesChange.textContent = `+${Math.floor(activeCompanies * 0.08)} this month`;
        }
        if (assessmentsChange) {
            assessmentsChange.textContent = `+${Math.floor(totalAssessments * 0.2)} this month`;
        }

        // Animate the individual metric values
        setTimeout(() => {
            animateIndividualMetrics();
        }, 300);
    }

    function animateIndividualMetrics() {
        const metricElements = [
            document.getElementById('total-admins'),
            document.getElementById('total-users'),
            document.getElementById('total-companies'),
            document.getElementById('total-assessments')
        ].filter(el => el && el.hasAttribute('data-target'));

        console.log(`Animating ${metricElements.length} individual metric elements`);

        metricElements.forEach((element, index) => {
            const target = parseInt(element.getAttribute('data-target'));
            const duration = 1000;
            const startTime = performance.now() + (index * 100);

            // Set initial value to 0 for animation
            element.textContent = '0';

            function updateValue(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                if (progress >= 0) {
                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                    const currentValue = Math.floor(target * easeOutQuart);
                    element.textContent = currentValue.toLocaleString();
                }

                if (progress < 1) {
                    requestAnimationFrame(updateValue);
                } else {
                    element.textContent = target.toLocaleString();
                }
            }

            requestAnimationFrame(updateValue);
        });
    }

    function animateMetricValues() {
        // Look for both old and new metric value selectors
        const metricValues = document.querySelectorAll('.metric-value[data-target], .overview-metric-value[data-target]');

        console.log(`Found ${metricValues.length} metric values to animate`);

        metricValues.forEach((element, index) => {
            const target = parseInt(element.dataset.target);
            const duration = 1000; // 1 second
            const startTime = performance.now() + (index * 100); // Stagger animations

            console.log(`Animating metric ${index}: target=${target}`);

            // Skip animation if no data available
            if (target === 0 || isNaN(target)) {
                return;
            }

            // Set initial value to 0 for animation
            element.textContent = '0';

            function updateValue(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                if (progress >= 0) {
                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                    const currentValue = Math.floor(target * easeOutQuart);
                    element.textContent = currentValue.toLocaleString();
                }

                if (progress < 1) {
                    requestAnimationFrame(updateValue);
                } else {
                    // Ensure final value is exactly the target
                    element.textContent = target.toLocaleString();
                }
            }

            requestAnimationFrame(updateValue);
        });
    }

    function initializeCharts() {
        console.log('Initializing charts with data:', dashboardData);

        // Check if required data is available
        if (!dashboardData.admins || !dashboardData.companies) {
            console.warn('Required data not available for charts');
            return;
        }

        // Initialize growth chart
        initializeGrowthChart();

        // Initialize completion rate chart only if assessment data is available
        if (dashboardData.assessments) {
            initializeCompletionChart();
        } else {
            console.warn('Assessment data not available for completion chart');
            // Create a placeholder chart or show loading state
            createPlaceholderCompletionChart();
        }
    }

    function initializeGrowthChart() {
        const ctx = document.getElementById('growth-chart');
        if (!ctx || !dashboardData.admins) return;

        // Get selected timeframe
        const timeframeSelect = document.getElementById('growth-timeframe');
        const selectedDays = timeframeSelect ? parseInt(timeframeSelect.value) : 30;

        // Prepare data for the selected timeframe
        const labels = [];
        const adminCounts = [];
        const userCounts = [];

        for (let i = selectedDays - 1; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));

            // Count admins created up to this date
            const adminsUpToDate = dashboardData.admins.filter(admin => {
                const createdDate = admin.createdAt ?
                    (admin.createdAt.toDate ? admin.createdAt.toDate() : new Date(admin.createdAt)) :
                    new Date(0);
                return createdDate <= date;
            }).length;

            adminCounts.push(adminsUpToDate);

            // Estimate user growth (placeholder)
            userCounts.push(Math.floor(adminsUpToDate * 2.5));
        }

        if (charts.growth) {
            charts.growth.destroy();
        }

        charts.growth = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Admin Accounts',
                    data: adminCounts,
                    borderColor: '#1547bb',
                    backgroundColor: 'rgba(21, 71, 187, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Total Users',
                    data: userCounts,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    function initializeCompletionChart() {
        const ctx = document.getElementById('completion-chart');
        if (!ctx) {
            console.error('Completion chart canvas not found');
            return;
        }

        // Always try to create the chart with available data, fallback to placeholder if needed
        let assessmentData = dashboardData.assessments;
        let totalPossible = 1;

        // Calculate total possible users from companies data
        if (dashboardData.companies && dashboardData.companies.length > 0) {
            totalPossible = dashboardData.companies.reduce((sum, c) => sum + (c.userCount || 0), 0);
        } else if (dashboardData.users && dashboardData.users.totalUsers) {
            totalPossible = dashboardData.users.totalUsers;
        }

        // If no assessment data, create with default values but still show a meaningful chart
        if (!assessmentData) {
            console.warn('Assessment data not available, creating chart with default values');
            assessmentData = {
                completedAssessments: Math.floor(totalPossible * 0.4), // 40% completion rate
                englishAssessments: Math.floor(totalPossible * 0.3),   // 30% english rate
                totalAssessments: Math.floor(totalPossible * 0.6)
            };
        }

        console.log('Completion chart data:', {
            assessmentData,
            totalPossible,
            completedAssessments: assessmentData.completedAssessments,
            englishAssessments: assessmentData.englishAssessments
        });

        const completedAssessments = assessmentData.completedAssessments || 0;
        const englishAssessments = assessmentData.englishAssessments || 0;
        const notCompleted = Math.max(0, totalPossible - completedAssessments);

        // Ensure we have meaningful data to display
        if (completedAssessments === 0 && englishAssessments === 0 && notCompleted === 0) {
            console.warn('No meaningful data for completion chart, creating placeholder');
            createPlaceholderCompletionChart();
            return;
        }

        if (charts.completion) {
            charts.completion.destroy();
        }

        charts.completion = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Digital Assessments', 'English Assessments', 'Not Completed'],
                datasets: [{
                    data: [
                        completedAssessments,
                        englishAssessments,
                        notCompleted
                    ],
                    backgroundColor: [
                        '#1547bb',
                        '#10b981',
                        '#e5e7eb'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 1000
                }
            }
        });

        console.log('Completion chart initialized successfully');
    }

    function createPlaceholderCompletionChart() {
        const ctx = document.getElementById('completion-chart');
        if (!ctx) return;

        if (charts.completion) {
            charts.completion.destroy();
        }

        // Create a more meaningful placeholder with sample data
        const sampleData = [45, 32, 23]; // Sample completion rates

        charts.completion = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Digital Assessments', 'English Assessments', 'Not Completed'],
                datasets: [{
                    data: sampleData,
                    backgroundColor: [
                        '#d1d5db', // Light gray for sample data
                        '#e5e7eb',
                        '#f3f4f6'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                return `${label}: ${value} (Sample data - Loading actual data...)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 800
                }
            }
        });
    }

    function loadSectionData(sectionName) {
        // Load section-specific data when switching sections
        switch (sectionName) {
            case 'admins':
                loadAdminSection();
                break;
            case 'companies':
                loadCompanySection();
                break;
            case 'users':
                loadUserSection();
                break;
            case 'assessments':
                loadAssessmentSection();
                break;
            case 'analytics':
                loadAnalyticsSection();
                break;
            case 'security':
                loadSecuritySection();
                break;
        }
    }

    function loadAdminSection() {
        console.log('Loading admin section...');

        if (!dashboardData.admins) {
            return;
        }

        const adminSection = document.getElementById('admins-section');
        const tableContainer = adminSection.querySelector('.table-container');

        // Create admin analytics table
        const adminTable = createAdminTable(dashboardData.admins);
        tableContainer.innerHTML = adminTable;

        // Add export functionality
        addExportFunctionality('admins', dashboardData.admins);
    }

    function createAdminTable(admins) {
        const now = new Date();

        // Sort admins by creation date (newest first)
        const sortedAdmins = [...admins].sort((a, b) => {
            const dateA = a.createdAt ? (a.createdAt.toDate ? a.createdAt.toDate() : new Date(a.createdAt)) : new Date(0);
            const dateB = b.createdAt ? (b.createdAt.toDate ? b.createdAt.toDate() : new Date(b.createdAt)) : new Date(0);
            return dateB - dateA;
        });

        let tableHTML = `
            <div class="table-header">
                <h3>Admin Account Details</h3>
                <div class="table-controls">
                    <input type="text" id="admin-search" placeholder="Search admins..." class="search-input">
                    <select id="admin-filter" class="filter-select">
                        <option value="all">All Admins</option>
                        <option value="freeTrial">Free Trial</option>
                        <option value="paid">Paid</option>
                        <option value="expired">Expired Trial</option>
                    </select>
                    <button id="export-admins" class="export-btn">Export CSV</button>
                </div>
            </div>
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Admin</th>
                            <th>Company</th>
                            <th>Email</th>
                            <th>Credits</th>
                            <th>Subscription</th>
                            <th>Trial Status</th>
                            <th>Lead Source</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="admin-table-body">
        `;

        sortedAdmins.forEach(admin => {
            const createdDate = admin.createdAt ?
                (admin.createdAt.toDate ? admin.createdAt.toDate() : new Date(admin.createdAt)) :
                new Date(0);

            const trialStatus = getTrialStatus(admin);
            const subscriptionBadge = getSubscriptionBadge(admin);

            tableHTML += `
                <tr class="admin-row" data-admin-id="${admin.id}">
                    <td>
                        <div class="admin-info">
                            <div class="admin-name">${admin.firstname} ${admin.lastname}</div>
                            <div class="admin-id">${admin.id}</div>
                        </div>
                    </td>
                    <td>
                        <div class="company-name">${admin.company || 'N/A'}</div>
                    </td>
                    <td>
                        <div class="admin-email">${admin.email}</div>
                    </td>
                    <td>
                        <div class="credits-info">
                            <span class="credits-count">${admin.credits}</span>
                            <span class="credits-label">credits</span>
                        </div>
                    </td>
                    <td>${subscriptionBadge}</td>
                    <td>${trialStatus}</td>
                    <td>
                        <span class="lead-source">${admin.leadSource || 'Direct'}</span>
                    </td>
                    <td>
                        <div class="date-info">
                            <div class="date-primary">${createdDate.toLocaleDateString()}</div>
                            <div class="date-secondary">${createdDate.toLocaleTimeString()}</div>
                        </div>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn view-btn" onclick="viewAdminDetails('${admin.id}')">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
            <div class="table-footer">
                <div class="table-info">
                    Showing ${sortedAdmins.length} admin accounts
                </div>
            </div>
        `;

        // Add event listeners after table is created
        setTimeout(() => {
            setupAdminTableEventListeners();
        }, 100);

        return tableHTML;
    }

    function getTrialStatus(admin) {
        if (admin.subscriptionType !== 'freeTrial') {
            return '<span class="status-badge paid">Paid Account</span>';
        }

        if (admin.isTrialExpired) {
            return '<span class="status-badge expired">Trial Expired</span>';
        }

        if (admin.trialDaysRemaining !== undefined) {
            const daysText = admin.trialDaysRemaining === 1 ? 'day' : 'days';
            return `<span class="status-badge trial">${admin.trialDaysRemaining} ${daysText} left</span>`;
        }

        return '<span class="status-badge active">Active Trial</span>';
    }

    function getSubscriptionBadge(admin) {
        if (admin.paid) {
            return '<span class="subscription-badge paid">Paid</span>';
        } else if (admin.subscriptionType === 'freeTrial') {
            return '<span class="subscription-badge trial">Free Trial</span>';
        } else {
            return '<span class="subscription-badge free">Free</span>';
        }
    }

    function setupAdminTableEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('admin-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                filterAdminTable(e.target.value, document.getElementById('admin-filter').value);
            });
        }

        // Filter functionality
        const filterSelect = document.getElementById('admin-filter');
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                filterAdminTable(document.getElementById('admin-search').value, e.target.value);
            });
        }

        // Export functionality
        const exportBtn = document.getElementById('export-admins');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                exportAdminData();
            });
        }
    }

    function filterAdminTable(searchTerm, filterType) {
        const rows = document.querySelectorAll('.admin-row');
        let visibleCount = 0;

        rows.forEach(row => {
            const adminName = row.querySelector('.admin-name').textContent.toLowerCase();
            const adminEmail = row.querySelector('.admin-email').textContent.toLowerCase();
            const companyName = row.querySelector('.company-name').textContent.toLowerCase();

            const matchesSearch = !searchTerm ||
                adminName.includes(searchTerm.toLowerCase()) ||
                adminEmail.includes(searchTerm.toLowerCase()) ||
                companyName.includes(searchTerm.toLowerCase());

            let matchesFilter = true;
            if (filterType !== 'all') {
                const subscriptionBadge = row.querySelector('.subscription-badge');
                const statusBadge = row.querySelector('.status-badge');

                switch (filterType) {
                    case 'freeTrial':
                        matchesFilter = subscriptionBadge && subscriptionBadge.classList.contains('trial');
                        break;
                    case 'paid':
                        matchesFilter = subscriptionBadge && subscriptionBadge.classList.contains('paid');
                        break;
                    case 'expired':
                        matchesFilter = statusBadge && statusBadge.classList.contains('expired');
                        break;
                }
            }

            const shouldShow = matchesSearch && matchesFilter;
            row.style.display = shouldShow ? '' : 'none';
            if (shouldShow) visibleCount++;
        });

        // Update table footer
        const tableInfo = document.querySelector('.table-info');
        if (tableInfo) {
            tableInfo.textContent = `Showing ${visibleCount} admin accounts`;
        }
    }

    function exportAdminData() {
        if (!dashboardData.admins) return;

        // Log export activity
        logActivity('admin_data_exported', {
            recordCount: dashboardData.admins.length,
            exportType: 'csv'
        });

        const csvData = [];
        csvData.push([
            'Name',
            'Email',
            'Company',
            'Credits',
            'Subscription Type',
            'Subscription Active',
            'Trial Days Remaining',
            'Lead Source',
            'Created Date',
            'Paid Status'
        ]);

        dashboardData.admins.forEach(admin => {
            const createdDate = admin.createdAt ?
                (admin.createdAt.toDate ? admin.createdAt.toDate() : new Date(admin.createdAt)) :
                new Date(0);

            csvData.push([
                `${admin.firstname} ${admin.lastname}`,
                admin.email,
                admin.company || 'N/A',
                admin.credits,
                admin.subscriptionType || 'N/A',
                admin.subscriptionActive ? 'Yes' : 'No',
                admin.trialDaysRemaining || 'N/A',
                admin.leadSource || 'Direct',
                createdDate.toLocaleDateString(),
                admin.paid ? 'Yes' : 'No'
            ]);
        });

        downloadCSV(csvData, 'admin-accounts-export.csv');
    }

    function loadCompanySection() {
        console.log('Loading company section...');

        if (!dashboardData.companies) {
            return;
        }

        const companySection = document.getElementById('companies-section');
        const tableContainer = companySection.querySelector('.table-container');

        // Create company analytics table
        const companyTable = createCompanyTable(dashboardData.companies);
        tableContainer.innerHTML = companyTable;

        // Add export functionality
        addExportFunctionality('companies', dashboardData.companies);
    }

    function createCompanyTable(companies) {
        // Sort companies by user count (highest first)
        const sortedCompanies = [...companies].sort((a, b) => b.userCount - a.userCount);

        let tableHTML = `
            <div class="table-header">
                <h3>Company Analytics</h3>
                <div class="table-controls">
                    <input type="text" id="company-search" placeholder="Search companies..." class="search-input">
                    <select id="company-filter" class="filter-select">
                        <option value="all">All Companies</option>
                        <option value="active">Active (>0 users)</option>
                        <option value="large">Large (>10 users)</option>
                        <option value="small">Small (1-10 users)</option>
                        <option value="empty">No Users</option>
                    </select>
                    <button id="export-companies" class="export-btn">Export CSV</button>
                </div>
            </div>
            <div class="company-stats">
                <div class="stat-card">
                    <div class="stat-value">${companies.length}</div>
                    <div class="stat-label">Total Companies</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${companies.filter(c => c.userCount > 0).length}</div>
                    <div class="stat-label">Active Companies</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${companies.reduce((sum, c) => sum + c.userCount, 0)}</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${Math.round(companies.reduce((sum, c) => sum + c.userCount, 0) / companies.filter(c => c.userCount > 0).length || 0)}</div>
                    <div class="stat-label">Avg Users/Company</div>
                </div>
            </div>
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Company Name</th>
                            <th>Admin Email</th>
                            <th>User Count</th>
                            <th>Created Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="company-table-body">
        `;

        sortedCompanies.forEach(company => {
            const createdDate = company.createdAt ?
                (company.createdAt.toDate ? company.createdAt.toDate() : new Date(company.createdAt)) :
                new Date(0);

            const statusBadge = getCompanyStatusBadge(company);

            tableHTML += `
                <tr class="company-row" data-company-id="${company.id}">
                    <td>
                        <div class="company-info">
                            <div class="company-name-display">${company.name}</div>
                            <div class="company-id">${company.id}</div>
                        </div>
                    </td>
                    <td>
                        <div class="admin-email">${company.adminEmail}</div>
                    </td>
                    <td>
                        <div class="user-count-info">
                            <span class="user-count">${company.userCount}</span>
                            <span class="user-label">users</span>
                        </div>
                    </td>
                    <td>
                        <div class="date-info">
                            <div class="date-primary">${createdDate.toLocaleDateString()}</div>
                            <div class="date-secondary">${createdDate.toLocaleTimeString()}</div>
                        </div>
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn view-btn" onclick="viewCompanyDetails('${company.id}')">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
            <div class="table-footer">
                <div class="table-info">
                    Showing ${sortedCompanies.length} companies
                </div>
            </div>
        `;

        // Add event listeners after table is created
        setTimeout(() => {
            setupCompanyTableEventListeners();
        }, 100);

        return tableHTML;
    }

    function getCompanyStatusBadge(company) {
        if (company.userCount === 0) {
            return '<span class="status-badge inactive">No Users</span>';
        } else if (company.userCount >= 10) {
            return '<span class="status-badge large">Large</span>';
        } else {
            return '<span class="status-badge active">Active</span>';
        }
    }

    function setupCompanyTableEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('company-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                filterCompanyTable(e.target.value, document.getElementById('company-filter').value);
            });
        }

        // Filter functionality
        const filterSelect = document.getElementById('company-filter');
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                filterCompanyTable(document.getElementById('company-search').value, e.target.value);
            });
        }

        // Export functionality
        const exportBtn = document.getElementById('export-companies');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                exportCompanyData();
            });
        }
    }

    function filterCompanyTable(searchTerm, filterType) {
        const rows = document.querySelectorAll('.company-row');
        let visibleCount = 0;

        rows.forEach(row => {
            const companyName = row.querySelector('.company-name-display').textContent.toLowerCase();
            const adminEmail = row.querySelector('.admin-email').textContent.toLowerCase();

            const matchesSearch = !searchTerm ||
                companyName.includes(searchTerm.toLowerCase()) ||
                adminEmail.includes(searchTerm.toLowerCase());

            let matchesFilter = true;
            if (filterType !== 'all') {
                const userCount = parseInt(row.querySelector('.user-count').textContent);

                switch (filterType) {
                    case 'active':
                        matchesFilter = userCount > 0;
                        break;
                    case 'large':
                        matchesFilter = userCount > 10;
                        break;
                    case 'small':
                        matchesFilter = userCount >= 1 && userCount <= 10;
                        break;
                    case 'empty':
                        matchesFilter = userCount === 0;
                        break;
                }
            }

            const shouldShow = matchesSearch && matchesFilter;
            row.style.display = shouldShow ? '' : 'none';
            if (shouldShow) visibleCount++;
        });

        // Update table footer
        const tableInfo = document.querySelector('.table-info');
        if (tableInfo) {
            tableInfo.textContent = `Showing ${visibleCount} companies`;
        }
    }

    function exportCompanyData() {
        if (!dashboardData.companies) return;

        const csvData = [];
        csvData.push([
            'Company Name',
            'Admin Email',
            'User Count',
            'Created Date',
            'Status'
        ]);

        dashboardData.companies.forEach(company => {
            const createdDate = company.createdAt ?
                (company.createdAt.toDate ? company.createdAt.toDate() : new Date(company.createdAt)) :
                new Date(0);

            const status = company.userCount === 0 ? 'No Users' :
                          company.userCount >= 10 ? 'Large' : 'Active';

            csvData.push([
                company.name,
                company.adminEmail,
                company.userCount,
                createdDate.toLocaleDateString(),
                status
            ]);
        });

        downloadCSV(csvData, 'company-analytics-export.csv');
    }

    function loadUserSection() {
        console.log('Loading user section...');

        if (!dashboardData.users || !dashboardData.companies) {
            return;
        }

        const userSection = document.getElementById('users-section');
        const tableContainer = userSection.querySelector('.table-container');

        // Create user analytics table
        const userTable = createUserTable(dashboardData.users, dashboardData.companies);
        tableContainer.innerHTML = userTable;
    }

    function createUserTable(userData, companies) {
        // Flatten all users from all companies
        const allUsers = [];
        companies.forEach(company => {
            company.users.forEach(user => {
                allUsers.push({
                    ...user,
                    companyName: company.name,
                    companyId: company.id
                });
            });
        });

        // Sort users by creation date (newest first)
        const sortedUsers = allUsers.sort((a, b) => {
            const dateA = a.createdAt ? (a.createdAt.toDate ? a.createdAt.toDate() : new Date(a.createdAt)) : new Date(0);
            const dateB = b.createdAt ? (b.createdAt.toDate ? b.createdAt.toDate() : new Date(b.createdAt)) : new Date(0);
            return dateB - dateA;
        });

        let tableHTML = `
            <div class="table-header">
                <h3>User Analytics</h3>
                <div class="table-controls">
                    <input type="text" id="user-search" placeholder="Search users..." class="search-input">
                    <select id="user-filter" class="filter-select">
                        <option value="all">All Users</option>
                        <option value="completed">Completed Assessments</option>
                        <option value="pending">Pending Assessments</option>
                    </select>
                    <button id="export-users" class="export-btn">Export CSV</button>
                </div>
            </div>
            <div class="user-stats">
                <div class="stat-card">
                    <div class="stat-value">${allUsers.length}</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${allUsers.filter(u => u.status === 'completed').length}</div>
                    <div class="stat-label">Completed Assessments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${allUsers.filter(u => u.status === 'started' || u.status === 'pending').length}</div>
                    <div class="stat-label">Pending Assessments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${Math.round((allUsers.filter(u => u.status === 'completed').length / allUsers.length) * 100) || 0}%</div>
                    <div class="stat-label">Completion Rate</div>
                </div>
            </div>
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Email</th>
                            <th>Company</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Created Date</th>
                        </tr>
                    </thead>
                    <tbody id="user-table-body">
        `;

        sortedUsers.forEach(user => {
            const createdDate = user.createdAt ?
                (user.createdAt.toDate ? user.createdAt.toDate() : new Date(user.createdAt)) :
                new Date(0);

            const statusBadge = getUserStatusBadge(user);

            tableHTML += `
                <tr class="user-row" data-user-id="${user.id}">
                    <td>
                        <div class="user-info">
                            <div class="user-name">${user.firstName} ${user.lastName}</div>
                            <div class="user-id">${user.id}</div>
                        </div>
                    </td>
                    <td>
                        <div class="user-email">${user.email}</div>
                    </td>
                    <td>
                        <div class="company-name">${user.companyName}</div>
                    </td>
                    <td>
                        <div class="user-role">${user.role || 'N/A'}</div>
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="date-info">
                            <div class="date-primary">${createdDate.toLocaleDateString()}</div>
                            <div class="date-secondary">${createdDate.toLocaleTimeString()}</div>
                        </div>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
            <div class="table-footer">
                <div class="table-info">
                    Showing ${sortedUsers.length} users
                </div>
            </div>
        `;

        // Add event listeners after table is created
        setTimeout(() => {
            setupUserTableEventListeners();
        }, 100);

        return tableHTML;
    }

    function getUserStatusBadge(user) {
        switch (user.status) {
            case 'completed':
                return '<span class="status-badge completed">Completed</span>';
            case 'started':
                return '<span class="status-badge started">In Progress</span>';
            case 'pending':
                return '<span class="status-badge pending">Pending</span>';
            default:
                return '<span class="status-badge unknown">Unknown</span>';
        }
    }

    function setupUserTableEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('user-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                filterUserTable(e.target.value, document.getElementById('user-filter').value);
            });
        }

        // Filter functionality
        const filterSelect = document.getElementById('user-filter');
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                filterUserTable(document.getElementById('user-search').value, e.target.value);
            });
        }

        // Export functionality
        const exportBtn = document.getElementById('export-users');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                exportUserData();
            });
        }
    }

    function filterUserTable(searchTerm, filterType) {
        const rows = document.querySelectorAll('.user-row');
        let visibleCount = 0;

        rows.forEach(row => {
            const userName = row.querySelector('.user-name').textContent.toLowerCase();
            const userEmail = row.querySelector('.user-email').textContent.toLowerCase();
            const companyName = row.querySelector('.company-name').textContent.toLowerCase();

            const matchesSearch = !searchTerm ||
                userName.includes(searchTerm.toLowerCase()) ||
                userEmail.includes(searchTerm.toLowerCase()) ||
                companyName.includes(searchTerm.toLowerCase());

            let matchesFilter = true;
            if (filterType !== 'all') {
                const statusBadge = row.querySelector('.status-badge');

                switch (filterType) {
                    case 'completed':
                        matchesFilter = statusBadge && statusBadge.classList.contains('completed');
                        break;
                    case 'pending':
                        matchesFilter = statusBadge && (statusBadge.classList.contains('pending') || statusBadge.classList.contains('started'));
                        break;
                }
            }

            const shouldShow = matchesSearch && matchesFilter;
            row.style.display = shouldShow ? '' : 'none';
            if (shouldShow) visibleCount++;
        });

        // Update table footer
        const tableInfo = document.querySelector('.table-info');
        if (tableInfo) {
            tableInfo.textContent = `Showing ${visibleCount} users`;
        }
    }

    function exportUserData() {
        if (!dashboardData.users || !dashboardData.companies) return;

        // Flatten all users from all companies
        const allUsers = [];
        dashboardData.companies.forEach(company => {
            company.users.forEach(user => {
                allUsers.push({
                    ...user,
                    companyName: company.name
                });
            });
        });

        const csvData = [];
        csvData.push([
            'Name',
            'Email',
            'Company',
            'Role',
            'Status',
            'Created Date'
        ]);

        allUsers.forEach(user => {
            const createdDate = user.createdAt ?
                (user.createdAt.toDate ? user.createdAt.toDate() : new Date(user.createdAt)) :
                new Date(0);

            csvData.push([
                `${user.firstName} ${user.lastName}`,
                user.email,
                user.companyName,
                user.role || 'N/A',
                user.status || 'Unknown',
                createdDate.toLocaleDateString()
            ]);
        });

        downloadCSV(csvData, 'user-analytics-export.csv');
    }

    function loadAssessmentSection() {
        console.log('Loading assessment section...');

        if (!dashboardData.assessments || !dashboardData.companies) {
            return;
        }

        const assessmentSection = document.getElementById('assessments-section');
        const tableContainer = assessmentSection.querySelector('.table-container');

        // Create assessment analytics
        const assessmentContent = createAssessmentAnalytics(dashboardData.assessments, dashboardData.companies);
        tableContainer.innerHTML = assessmentContent;

        // Initialize assessment charts
        setTimeout(() => {
            initializeAssessmentCharts();
        }, 100);
    }

    function createAssessmentAnalytics(assessmentData, companies) {
        const totalUsers = companies.reduce((sum, c) => sum + c.userCount, 0);
        const completionRate = totalUsers > 0 ? (assessmentData.completedAssessments / totalUsers) * 100 : 0;
        const englishRate = totalUsers > 0 ? (assessmentData.englishAssessments / totalUsers) * 100 : 0;

        let analyticsHTML = `
            <div class="assessment-analytics">
                <div class="analytics-header">
                    <h3>Assessment Analytics Overview</h3>
                    <div class="date-filter">
                        <select id="assessment-period">
                            <option value="7">Last 7 days</option>
                            <option value="30" selected>Last 30 days</option>
                            <option value="90">Last 90 days</option>
                            <option value="all">All time</option>
                        </select>
                    </div>
                </div>

                <!-- Assessment Stats -->
                <div class="assessment-stats">
                    <div class="stat-card">
                        <div class="stat-value">${assessmentData.totalAssessments}</div>
                        <div class="stat-label">Total Assessments</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${assessmentData.completedAssessments}</div>
                        <div class="stat-label">Digital Assessments</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${assessmentData.englishAssessments}</div>
                        <div class="stat-label">English Assessments</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${Math.round(completionRate)}%</div>
                        <div class="stat-label">Completion Rate</div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="assessment-charts">
                    <div class="chart-row">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h4>Assessment Types Distribution</h4>
                            </div>
                            <div class="chart-container">
                                <canvas id="assessment-types-chart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h4>Completion Rate by Company</h4>
                            </div>
                            <div class="chart-container">
                                <canvas id="company-completion-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="chart-row">
                        <div class="chart-card full-width">
                            <div class="chart-header">
                                <h4>Assessment Completion Trends</h4>
                                <div class="chart-controls">
                                    <select id="trend-type">
                                        <option value="daily">Daily</option>
                                        <option value="weekly" selected>Weekly</option>
                                        <option value="monthly">Monthly</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="assessment-trends-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Company Assessment Breakdown -->
                <div class="company-assessment-breakdown">
                    <div class="breakdown-header">
                        <h4>Assessment Breakdown by Company</h4>
                        <button id="export-assessment-data" class="export-btn">Export Data</button>
                    </div>
                    <div class="breakdown-table">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Company</th>
                                    <th>Total Users</th>
                                    <th>Digital Completed</th>
                                    <th>English Completed</th>
                                    <th>Completion Rate</th>
                                    <th>English Rate</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        // Add company breakdown rows
        companies.forEach(company => {
            const digitalCompleted = assessmentData.assessmentsByCompany[company.name]?.completed || 0;
            const englishCompleted = assessmentData.assessmentsByCompany[company.name]?.english || 0;
            const companyCompletionRate = company.userCount > 0 ? (digitalCompleted / company.userCount) * 100 : 0;
            const companyEnglishRate = company.userCount > 0 ? (englishCompleted / company.userCount) * 100 : 0;

            analyticsHTML += `
                <tr>
                    <td>
                        <div class="company-name">${company.name}</div>
                    </td>
                    <td>
                        <div class="user-count">${company.userCount}</div>
                    </td>
                    <td>
                        <div class="assessment-count">${digitalCompleted}</div>
                    </td>
                    <td>
                        <div class="assessment-count">${englishCompleted}</div>
                    </td>
                    <td>
                        <div class="completion-rate ${companyCompletionRate >= 50 ? 'good' : companyCompletionRate >= 25 ? 'fair' : 'poor'}">
                            ${Math.round(companyCompletionRate)}%
                        </div>
                    </td>
                    <td>
                        <div class="completion-rate ${companyEnglishRate >= 50 ? 'good' : companyEnglishRate >= 25 ? 'fair' : 'poor'}">
                            ${Math.round(companyEnglishRate)}%
                        </div>
                    </td>
                </tr>
            `;
        });

        analyticsHTML += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        // Add event listeners
        setTimeout(() => {
            setupAssessmentEventListeners();
        }, 100);

        return analyticsHTML;
    }

    function setupAssessmentEventListeners() {
        // Period filter
        const periodSelect = document.getElementById('assessment-period');
        if (periodSelect) {
            periodSelect.addEventListener('change', (e) => {
                // Reload assessment data for selected period
                console.log('Assessment period changed:', e.target.value);
                // Implementation would filter data by date range
            });
        }

        // Trend type filter
        const trendSelect = document.getElementById('trend-type');
        if (trendSelect) {
            // Ensure the default value is set to weekly
            if (!trendSelect.value) {
                trendSelect.value = 'weekly';
            }

            trendSelect.addEventListener('change', (e) => {
                console.log('Trend type changed to:', e.target.value);
                updateAssessmentTrendsChart(e.target.value);
            });
        }

        // Export button
        const exportBtn = document.getElementById('export-assessment-data');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                exportAssessmentData();
            });
        }
    }

    function initializeAssessmentCharts() {
        initializeAssessmentTypesChart();
        initializeCompanyCompletionChart();
        initializeAssessmentTrendsChart();
    }

    function initializeAssessmentTypesChart() {
        const ctx = document.getElementById('assessment-types-chart');
        if (!ctx) {
            console.error('Assessment types chart canvas not found');
            return;
        }

        if (!dashboardData.assessments) {
            console.warn('Assessment data not available, creating placeholder chart');
            createPlaceholderAssessmentTypesChart();
            return;
        }

        const assessmentData = dashboardData.assessments;
        const totalUsers = dashboardData.companies ? dashboardData.companies.reduce((sum, c) => sum + c.userCount, 0) : 100;
        const notStarted = Math.max(0, totalUsers - assessmentData.completedAssessments);

        if (charts.assessmentTypes) {
            charts.assessmentTypes.destroy();
        }

        charts.assessmentTypes = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Digital Completed', 'English Completed', 'Not Started'],
                datasets: [{
                    data: [
                        assessmentData.completedAssessments,
                        assessmentData.englishAssessments,
                        notStarted
                    ],
                    backgroundColor: [
                        '#1547bb',
                        '#10b981',
                        '#e5e7eb'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });

        console.log('Assessment types chart initialized successfully');
    }

    function createPlaceholderAssessmentTypesChart() {
        const ctx = document.getElementById('assessment-types-chart');
        if (!ctx) return;

        if (charts.assessmentTypes) {
            charts.assessmentTypes.destroy();
        }

        // Create sample data for demonstration
        charts.assessmentTypes = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Digital Completed', 'English Completed', 'Not Started'],
                datasets: [{
                    data: [45, 32, 23],
                    backgroundColor: [
                        '#e5e7eb',
                        '#d1d5db',
                        '#f3f4f6'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function() {
                                return 'Sample data - Loading actual data...';
                            }
                        }
                    }
                }
            }
        });
    }

    function initializeCompanyCompletionChart() {
        const ctx = document.getElementById('company-completion-chart');
        if (!ctx) {
            console.error('Company completion chart canvas not found');
            return;
        }

        // Always try to create a meaningful chart, even with partial data
        if (!dashboardData.companies || dashboardData.companies.length === 0) {
            console.warn('No company data available, creating placeholder chart');
            createPlaceholderCompanyCompletionChart();
            return;
        }

        // Get top 10 companies by user count
        const topCompanies = [...dashboardData.companies]
            .sort((a, b) => b.userCount - a.userCount)
            .slice(0, 10);

        const labels = topCompanies.map(c => c.name.length > 15 ? c.name.substring(0, 15) + '...' : c.name);

        // Calculate completion rates - use assessment data if available, otherwise use mock data
        const completionRates = topCompanies.map(company => {
            if (dashboardData.assessments && dashboardData.assessments.assessmentsByCompany) {
                const digitalCompleted = dashboardData.assessments.assessmentsByCompany[company.name]?.completed || 0;
                return company.userCount > 0 ? (digitalCompleted / company.userCount) * 100 : 0;
            } else {
                // Generate realistic mock completion rates if assessment data not available
                return Math.floor(Math.random() * 60) + 20; // 20-80% completion rate
            }
        });

        // Determine if we're showing real or mock data for styling
        const isRealData = dashboardData.assessments && dashboardData.assessments.assessmentsByCompany;
        const backgroundColor = isRealData ? '#1547bb' : '#9ca3af'; // Gray for mock data
        const borderColor = isRealData ? '#0f3699' : '#6b7280';

        if (charts.companyCompletion) {
            charts.companyCompletion.destroy();
        }

        charts.companyCompletion = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Completion Rate (%)',
                    data: completionRates,
                    backgroundColor: backgroundColor,
                    borderColor: borderColor,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed.y;
                                const suffix = isRealData ? '' : ' (Sample data)';
                                return `Completion Rate: ${value.toFixed(1)}%${suffix}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45
                        }
                    }
                }
            }
        });

        console.log('Company completion chart initialized successfully', isRealData ? 'with real data' : 'with mock data');
    }

    function createPlaceholderCompanyCompletionChart() {
        const ctx = document.getElementById('company-completion-chart');
        if (!ctx) return;

        if (charts.companyCompletion) {
            charts.companyCompletion.destroy();
        }

        // Create sample data for demonstration
        const sampleCompanies = ['TechCorp', 'DataSoft', 'InnovateLtd', 'GlobalTech', 'DigitalPlus'];
        const sampleRates = [85, 72, 94, 68, 81];

        charts.companyCompletion = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: sampleCompanies,
                datasets: [{
                    label: 'Completion Rate (%)',
                    data: sampleRates,
                    backgroundColor: '#e5e7eb',
                    borderColor: '#d1d5db',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function() {
                                return 'Sample data - Loading actual data...';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45
                        }
                    }
                }
            }
        });
    }

    function initializeAssessmentTrendsChart() {
        console.log('Initializing Assessment Trends Chart...');

        // Check if the canvas element exists
        const ctx = document.getElementById('assessment-trends-chart');
        if (!ctx) {
            console.warn('Assessment trends chart canvas not found, retrying in 100ms...');
            setTimeout(() => {
                initializeAssessmentTrendsChart();
            }, 100);
            return;
        }

        // Get the current selected trend type from the dropdown, default to weekly
        const trendSelect = document.getElementById('trend-type');
        const selectedTrend = trendSelect ? trendSelect.value : 'weekly';

        console.log('Initializing with trend type:', selectedTrend);

        // Initialize with the selected or default trend type
        updateAssessmentTrendsChart(selectedTrend);
    }

    function updateAssessmentTrendsChart(trendType) {
        console.log('Updating Assessment Trends Chart with type:', trendType);

        const ctx = document.getElementById('assessment-trends-chart');
        if (!ctx) {
            console.error('Assessment trends chart canvas not found');
            return;
        }

        // Generate sample trend data (in a real implementation, this would come from Firebase)
        const periods = trendType === 'daily' ? 30 : trendType === 'weekly' ? 12 : 6;
        const labels = [];
        const digitalData = [];
        const englishData = [];

        for (let i = periods - 1; i >= 0; i--) {
            let label, digitalValue, englishValue;

            if (trendType === 'daily') {
                const date = new Date();
                date.setDate(date.getDate() - i);
                label = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                digitalValue = Math.floor(Math.random() * 10) + 1;
                englishValue = Math.floor(Math.random() * 8) + 1;
            } else if (trendType === 'weekly') {
                label = `Week ${periods - i}`;
                digitalValue = Math.floor(Math.random() * 50) + 10;
                englishValue = Math.floor(Math.random() * 40) + 5;
            } else {
                const date = new Date();
                date.setMonth(date.getMonth() - i);
                label = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
                digitalValue = Math.floor(Math.random() * 200) + 50;
                englishValue = Math.floor(Math.random() * 150) + 30;
            }

            labels.push(label);
            digitalData.push(digitalValue);
            englishData.push(englishValue);
        }

        if (charts.assessmentTrends) {
            charts.assessmentTrends.destroy();
        }

        charts.assessmentTrends = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Digital Assessments',
                    data: digitalData,
                    borderColor: '#1547bb',
                    backgroundColor: 'rgba(21, 71, 187, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'English Assessments',
                    data: englishData,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.parsed.y;
                                return `${label}: ${value} (Sample data)`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        console.log('Assessment Trends Chart updated successfully with', trendType, 'data');
    }

    function exportAssessmentData() {
        if (!dashboardData.companies || !dashboardData.assessments) return;

        const csvData = [];
        csvData.push([
            'Company',
            'Total Users',
            'Digital Assessments Completed',
            'English Assessments Completed',
            'Digital Completion Rate (%)',
            'English Completion Rate (%)'
        ]);

        dashboardData.companies.forEach(company => {
            const digitalCompleted = dashboardData.assessments.assessmentsByCompany[company.name]?.completed || 0;
            const englishCompleted = dashboardData.assessments.assessmentsByCompany[company.name]?.english || 0;
            const digitalRate = company.userCount > 0 ? Math.round((digitalCompleted / company.userCount) * 100) : 0;
            const englishRate = company.userCount > 0 ? Math.round((englishCompleted / company.userCount) * 100) : 0;

            csvData.push([
                company.name,
                company.userCount,
                digitalCompleted,
                englishCompleted,
                digitalRate,
                englishRate
            ]);
        });

        downloadCSV(csvData, 'assessment-analytics-export.csv');
    }

    function loadAnalyticsSection() {
        console.log('Loading analytics section...');

        const analyticsSection = document.getElementById('analytics-section');
        const analyticsContent = analyticsSection.querySelector('.analytics-content');

        // Create advanced analytics content
        const advancedAnalytics = createAdvancedAnalytics();
        analyticsContent.innerHTML = advancedAnalytics;

        // Initialize advanced charts
        setTimeout(() => {
            initializeAdvancedCharts();
        }, 100);
    }

    function createAdvancedAnalytics() {
        return `
            <div class="advanced-analytics">
                <div class="analytics-grid">
                    <!-- Credit Usage Analytics -->
                    <div class="analytics-card">
                        <div class="card-header">
                            <h4>Credit Usage Analytics</h4>
                            <select id="credit-period">
                                <option value="30">Last 30 days</option>
                                <option value="90">Last 90 days</option>
                                <option value="all">All time</option>
                            </select>
                        </div>
                        <div class="chart-container">
                            <canvas id="credit-usage-chart"></canvas>
                        </div>
                        <div class="card-stats">
                            <div class="stat-item">
                                <span class="stat-label">Total Credits Distributed:</span>
                                <span class="stat-value" id="total-credits">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Average per Admin:</span>
                                <span class="stat-value" id="avg-credits">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- Lead Source Analytics -->
                    <div class="analytics-card">
                        <div class="card-header">
                            <h4>Lead Source Distribution</h4>
                        </div>
                        <div class="chart-container">
                            <canvas id="lead-source-chart"></canvas>
                        </div>
                        <div class="lead-source-stats" id="lead-source-stats">
                            <!-- Lead source statistics will be populated here -->
                        </div>
                    </div>

                    <!-- Platform Growth Analytics -->
                    <div class="analytics-card full-width">
                        <div class="card-header">
                            <h4>Platform Growth Metrics</h4>
                            <div class="growth-controls">
                                <select id="growth-metric">
                                    <option value="admins">Admin Signups</option>
                                    <option value="companies">Company Growth</option>
                                    <option value="users">User Growth</option>
                                </select>
                                <select id="growth-timeframe">
                                    <option value="daily">Daily</option>
                                    <option value="weekly" selected>Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                            </div>
                        </div>
                        <div class="chart-container large">
                            <canvas id="growth-metrics-chart"></canvas>
                        </div>
                    </div>

                    <!-- Subscription Analytics -->
                    <div class="analytics-card">
                        <div class="card-header">
                            <h4>Subscription Analytics</h4>
                        </div>
                        <div class="subscription-breakdown">
                            <div class="breakdown-item">
                                <div class="breakdown-label">Free Trial Users</div>
                                <div class="breakdown-value" id="free-trial-count">-</div>
                                <div class="breakdown-percentage" id="free-trial-percentage">-</div>
                            </div>
                            <div class="breakdown-item">
                                <div class="breakdown-label">Paid Users</div>
                                <div class="breakdown-value" id="paid-count">-</div>
                                <div class="breakdown-percentage" id="paid-percentage">-</div>
                            </div>
                            <div class="breakdown-item">
                                <div class="breakdown-label">Expired Trials</div>
                                <div class="breakdown-value" id="expired-count">-</div>
                                <div class="breakdown-percentage" id="expired-percentage">-</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="subscription-chart"></canvas>
                        </div>
                    </div>

                    <!-- Activity Heatmap -->
                    <div class="analytics-card">
                        <div class="card-header">
                            <h4>Activity Heatmap</h4>
                            <span class="info-tooltip" title="Shows signup activity by day of week and hour">ℹ</span>
                        </div>
                        <div class="heatmap-container">
                            <div class="heatmap-placeholder">
                                <p>Activity heatmap visualization</p>
                                <small>Shows peak signup times and days</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Export All Data Section -->
                <div class="export-section">
                    <div class="export-header">
                        <h4>Data Export</h4>
                        <p>Export comprehensive platform data for external analysis</p>
                    </div>
                    <div class="export-options">
                        <button class="export-option" onclick="exportAllData('csv')">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <div>
                                <div class="option-title">Export to CSV</div>
                                <div class="option-description">Complete dataset in CSV format</div>
                            </div>
                        </button>

                        <button class="export-option" onclick="exportAllData('json')">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            <div>
                                <div class="option-title">Export to JSON</div>
                                <div class="option-description">Structured data for API integration</div>
                            </div>
                        </button>

                        <button class="export-option" onclick="generateReport()">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a4 4 0 01-4-4V5a4 4 0 014-4h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a4 4 0 01-4 4z"></path>
                            </svg>
                            <div>
                                <div class="option-title">Generate Report</div>
                                <div class="option-description">Comprehensive analytics report</div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    function initializeAdvancedCharts() {
        initializeCreditUsageChart();
        initializeLeadSourceChart();
        initializeGrowthMetricsChart();
        initializeSubscriptionChart();
        updateSubscriptionBreakdown();
    }

    function initializeCreditUsageChart() {
        const ctx = document.getElementById('credit-usage-chart');
        if (!ctx || !dashboardData.admins) return;

        // Calculate credit distribution
        const creditRanges = {
            '0-5': 0,
            '6-25': 0,
            '26-100': 0,
            '100+': 0
        };

        let totalCredits = 0;
        dashboardData.admins.forEach(admin => {
            const credits = admin.credits || 0;
            totalCredits += credits;

            if (credits <= 5) creditRanges['0-5']++;
            else if (credits <= 25) creditRanges['6-25']++;
            else if (credits <= 100) creditRanges['26-100']++;
            else creditRanges['100+']++;
        });

        // Update stats
        document.getElementById('total-credits').textContent = totalCredits.toLocaleString();
        document.getElementById('avg-credits').textContent = Math.round(totalCredits / dashboardData.admins.length);

        if (charts.creditUsage) {
            charts.creditUsage.destroy();
        }

        charts.creditUsage = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: Object.keys(creditRanges),
                datasets: [{
                    label: 'Number of Admins',
                    data: Object.values(creditRanges),
                    backgroundColor: '#1547bb',
                    borderColor: '#0f3699',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    function initializeLeadSourceChart() {
        const ctx = document.getElementById('lead-source-chart');
        if (!ctx || !dashboardData.admins) return;

        // Calculate lead source distribution
        const leadSources = {};
        dashboardData.admins.forEach(admin => {
            const source = admin.leadSource || 'Direct';
            leadSources[source] = (leadSources[source] || 0) + 1;
        });

        // Update lead source stats
        const statsContainer = document.getElementById('lead-source-stats');
        let statsHTML = '';
        Object.entries(leadSources).forEach(([source, count]) => {
            const percentage = Math.round((count / dashboardData.admins.length) * 100);
            statsHTML += `
                <div class="lead-stat-item">
                    <span class="lead-source-name">${source}</span>
                    <span class="lead-count">${count} (${percentage}%)</span>
                </div>
            `;
        });
        statsContainer.innerHTML = statsHTML;

        if (charts.leadSource) {
            charts.leadSource.destroy();
        }

        const colors = ['#1547bb', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

        charts.leadSource = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: Object.keys(leadSources),
                datasets: [{
                    data: Object.values(leadSources),
                    backgroundColor: colors.slice(0, Object.keys(leadSources).length),
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    }

    function initializeGrowthMetricsChart() {
        updateGrowthMetricsChart('admins', 'weekly');

        // Add event listeners
        const metricSelect = document.getElementById('growth-metric');
        const timeframeSelect = document.getElementById('growth-timeframe');

        if (metricSelect && timeframeSelect) {
            metricSelect.addEventListener('change', () => {
                updateGrowthMetricsChart(metricSelect.value, timeframeSelect.value);
            });

            timeframeSelect.addEventListener('change', () => {
                updateGrowthMetricsChart(metricSelect.value, timeframeSelect.value);
            });
        }
    }

    function updateGrowthMetricsChart(metric, timeframe) {
        const ctx = document.getElementById('growth-metrics-chart');
        if (!ctx) return;

        // Generate sample growth data (in real implementation, this would come from Firebase)
        const periods = timeframe === 'daily' ? 30 : timeframe === 'weekly' ? 12 : 6;
        const labels = [];
        const data = [];

        for (let i = periods - 1; i >= 0; i--) {
            let label, value;

            if (timeframe === 'daily') {
                const date = new Date();
                date.setDate(date.getDate() - i);
                label = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                value = Math.floor(Math.random() * 5) + 1;
            } else if (timeframe === 'weekly') {
                label = `Week ${periods - i}`;
                value = Math.floor(Math.random() * 20) + 5;
            } else {
                const date = new Date();
                date.setMonth(date.getMonth() - i);
                label = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
                value = Math.floor(Math.random() * 100) + 20;
            }

            labels.push(label);
            data.push(value);
        }

        if (charts.growthMetrics) {
            charts.growthMetrics.destroy();
        }

        const metricLabels = {
            'admins': 'New Admin Signups',
            'companies': 'New Companies',
            'users': 'New Users'
        };

        charts.growthMetrics = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: metricLabels[metric],
                    data: data,
                    borderColor: '#1547bb',
                    backgroundColor: 'rgba(21, 71, 187, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    function initializeSubscriptionChart() {
        const ctx = document.getElementById('subscription-chart');
        if (!ctx || !dashboardData.admins) return;

        const subscriptionData = {
            freeTrial: 0,
            paid: 0,
            expired: 0
        };

        dashboardData.admins.forEach(admin => {
            if (admin.paid) {
                subscriptionData.paid++;
            } else if (admin.isTrialExpired) {
                subscriptionData.expired++;
            } else if (admin.subscriptionType === 'freeTrial') {
                subscriptionData.freeTrial++;
            }
        });

        if (charts.subscription) {
            charts.subscription.destroy();
        }

        charts.subscription = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Free Trial', 'Paid', 'Expired'],
                datasets: [{
                    data: [subscriptionData.freeTrial, subscriptionData.paid, subscriptionData.expired],
                    backgroundColor: ['#fbbf24', '#10b981', '#ef4444'],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    }

    function updateSubscriptionBreakdown() {
        if (!dashboardData.admins) return;

        const total = dashboardData.admins.length;
        let freeTrial = 0, paid = 0, expired = 0;

        dashboardData.admins.forEach(admin => {
            if (admin.paid) {
                paid++;
            } else if (admin.isTrialExpired) {
                expired++;
            } else if (admin.subscriptionType === 'freeTrial') {
                freeTrial++;
            }
        });

        document.getElementById('free-trial-count').textContent = freeTrial;
        document.getElementById('free-trial-percentage').textContent = `${Math.round((freeTrial / total) * 100)}%`;

        document.getElementById('paid-count').textContent = paid;
        document.getElementById('paid-percentage').textContent = `${Math.round((paid / total) * 100)}%`;

        document.getElementById('expired-count').textContent = expired;
        document.getElementById('expired-percentage').textContent = `${Math.round((expired / total) * 100)}%`;
    }

    function showError(message) {
        // Simple error display - could be enhanced with a proper modal
        alert(message);
    }

    // Utility functions
    function downloadCSV(data, filename) {
        const csvContent = data.map(row =>
            row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
        ).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    function addExportFunctionality(type, data) {
        // Export functionality is handled in the specific section functions
        console.log(`Export functionality added for ${type}`);
    }

    // Global function for viewing admin details (called from HTML)
    window.viewAdminDetails = function(adminId) {
        const admin = dashboardData.admins.find(a => a.id === adminId);
        if (!admin) return;

        // Log admin detail view
        logActivity('admin_details_viewed', {
            adminId: adminId,
            adminEmail: admin.email,
            adminCompany: admin.company
        });

        // Create and show admin details modal
        showAdminDetailsModal(admin);
    };

    // Global function for viewing company details (called from HTML)
    window.viewCompanyDetails = function(companyId) {
        const company = dashboardData.companies.find(c => c.id === companyId);
        if (!company) return;

        // Create and show company details modal
        showCompanyDetailsModal(company);
    };

    function showCompanyDetailsModal(company) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content company-details-modal">
                <div class="modal-header">
                    <h3>Company Details</h3>
                    <button class="modal-close" onclick="closeModal(this)">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="company-details-grid">
                        <div class="detail-section">
                            <h4>Company Information</h4>
                            <div class="detail-item">
                                <label>Company Name:</label>
                                <span>${company.name}</span>
                            </div>
                            <div class="detail-item">
                                <label>Admin Email:</label>
                                <span>${company.adminEmail}</span>
                            </div>
                            <div class="detail-item">
                                <label>Created Date:</label>
                                <span>${company.createdAt ? (company.createdAt.toDate ? company.createdAt.toDate() : new Date(company.createdAt)).toLocaleString() : 'N/A'}</span>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h4>User Statistics</h4>
                            <div class="detail-item">
                                <label>Total Users:</label>
                                <span class="credits-highlight">${company.userCount}</span>
                            </div>
                            <div class="detail-item">
                                <label>Completed Assessments:</label>
                                <span>${company.users.filter(u => u.status === 'completed').length}</span>
                            </div>
                            <div class="detail-item">
                                <label>Pending Assessments:</label>
                                <span>${company.users.filter(u => u.status === 'started' || u.status === 'pending').length}</span>
                            </div>
                            <div class="detail-item">
                                <label>Completion Rate:</label>
                                <span>${company.userCount > 0 ? Math.round((company.users.filter(u => u.status === 'completed').length / company.userCount) * 100) : 0}%</span>
                            </div>
                        </div>
                    </div>

                    ${company.users.length > 0 ? `
                    <div class="detail-section full-width">
                        <h4>Users (${company.users.length})</h4>
                        <div class="users-list">
                            ${company.users.map(user => `
                                <div class="user-item">
                                    <div class="user-info">
                                        <div class="user-name">${user.firstName} ${user.lastName}</div>
                                        <div class="user-email">${user.email}</div>
                                    </div>
                                    <div class="user-status">
                                        ${getUserStatusBadge(user)}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Show modal with animation
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.modal-content').style.transform = 'scale(1)';
        }, 10);
    }

    function showAdminDetailsModal(admin) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content admin-details-modal">
                <div class="modal-header">
                    <h3>Admin Account Details</h3>
                    <button class="modal-close" onclick="closeModal(this)">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="admin-details-grid">
                        <div class="detail-section">
                            <h4>Personal Information</h4>
                            <div class="detail-item">
                                <label>Name:</label>
                                <span>${admin.firstname} ${admin.lastname}</span>
                            </div>
                            <div class="detail-item">
                                <label>Email:</label>
                                <span>${admin.email}</span>
                            </div>
                            <div class="detail-item">
                                <label>Company:</label>
                                <span>${admin.company || 'N/A'}</span>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h4>Account Status</h4>
                            <div class="detail-item">
                                <label>Credits:</label>
                                <span class="credits-highlight">${admin.credits}</span>
                            </div>
                            <div class="detail-item">
                                <label>Subscription:</label>
                                <span>${getSubscriptionBadge(admin)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Trial Status:</label>
                                <span>${getTrialStatus(admin)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Paid Status:</label>
                                <span class="${admin.paid ? 'text-green-600' : 'text-gray-600'}">${admin.paid ? 'Paid' : 'Free'}</span>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h4>Account History</h4>
                            <div class="detail-item">
                                <label>Created:</label>
                                <span>${admin.createdAt ? (admin.createdAt.toDate ? admin.createdAt.toDate() : new Date(admin.createdAt)).toLocaleString() : 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Lead Source:</label>
                                <span>${admin.leadSource || 'Direct'}</span>
                            </div>
                            ${admin.subscriptionEndDate ? `
                            <div class="detail-item">
                                <label>Trial End Date:</label>
                                <span>${(admin.subscriptionEndDate.toDate ? admin.subscriptionEndDate.toDate() : new Date(admin.subscriptionEndDate)).toLocaleString()}</span>
                            </div>
                            ` : ''}
                        </div>

                        ${admin.referralStats ? `
                        <div class="detail-section">
                            <h4>Referral Statistics</h4>
                            <div class="detail-item">
                                <label>Total Referrals:</label>
                                <span>${admin.referralStats.totalReferrals || 0}</span>
                            </div>
                            <div class="detail-item">
                                <label>Successful Referrals:</label>
                                <span>${admin.referralStats.successfulReferrals || 0}</span>
                            </div>
                            <div class="detail-item">
                                <label>Credits Earned:</label>
                                <span>${admin.referralStats.creditsEarned || 0}</span>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Show modal with animation
        setTimeout(() => {
            modal.style.opacity = '1';
            modal.querySelector('.modal-content').style.transform = 'scale(1)';
        }, 10);
    }

    // Global function for closing modals
    window.closeModal = function(button) {
        const modal = button.closest('.modal-overlay');
        if (modal) {
            modal.style.opacity = '0';
            modal.querySelector('.modal-content').style.transform = 'scale(0.95)';
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        }
    };

    // Global export functions
    window.exportAllData = function(format) {
        if (!dashboardData.admins || !dashboardData.companies) {
            showError('Data not loaded yet. Please wait and try again.');
            return;
        }

        if (format === 'csv') {
            exportComprehensiveCSV();
        } else if (format === 'json') {
            exportComprehensiveJSON();
        }
    };

    window.generateReport = function() {
        if (!dashboardData.admins || !dashboardData.companies) {
            showError('Data not loaded yet. Please wait and try again.');
            return;
        }

        generateAnalyticsReport();
    };

    function exportComprehensiveCSV() {
        // Create a comprehensive CSV with multiple sheets worth of data
        const timestamp = new Date().toISOString().split('T')[0];

        // Admin data
        const adminCSV = [];
        adminCSV.push(['=== ADMIN ACCOUNTS ===']);
        adminCSV.push([
            'Name', 'Email', 'Company', 'Credits', 'Subscription Type',
            'Subscription Active', 'Trial Days Remaining', 'Lead Source',
            'Created Date', 'Paid Status', 'Referral Code'
        ]);

        dashboardData.admins.forEach(admin => {
            const createdDate = admin.createdAt ?
                (admin.createdAt.toDate ? admin.createdAt.toDate() : new Date(admin.createdAt)) :
                new Date(0);

            adminCSV.push([
                `${admin.firstname} ${admin.lastname}`,
                admin.email,
                admin.company || 'N/A',
                admin.credits,
                admin.subscriptionType || 'N/A',
                admin.subscriptionActive ? 'Yes' : 'No',
                admin.trialDaysRemaining || 'N/A',
                admin.leadSource || 'Direct',
                createdDate.toLocaleDateString(),
                admin.paid ? 'Yes' : 'No',
                admin.referralCode || 'N/A'
            ]);
        });

        // Company data
        adminCSV.push(['']); // Empty row
        adminCSV.push(['=== COMPANIES ===']);
        adminCSV.push(['Company Name', 'Admin Email', 'User Count', 'Created Date', 'Status']);

        dashboardData.companies.forEach(company => {
            const createdDate = company.createdAt ?
                (company.createdAt.toDate ? company.createdAt.toDate() : new Date(company.createdAt)) :
                new Date(0);

            const status = company.userCount === 0 ? 'No Users' :
                          company.userCount >= 10 ? 'Large' : 'Active';

            adminCSV.push([
                company.name,
                company.adminEmail,
                company.userCount,
                createdDate.toLocaleDateString(),
                status
            ]);
        });

        // Assessment data
        if (dashboardData.assessments) {
            adminCSV.push(['']); // Empty row
            adminCSV.push(['=== ASSESSMENT ANALYTICS ===']);
            adminCSV.push(['Company', 'Total Users', 'Digital Completed', 'English Completed', 'Digital Rate %', 'English Rate %']);

            dashboardData.companies.forEach(company => {
                const digitalCompleted = dashboardData.assessments.assessmentsByCompany[company.name]?.completed || 0;
                const englishCompleted = dashboardData.assessments.assessmentsByCompany[company.name]?.english || 0;
                const digitalRate = company.userCount > 0 ? Math.round((digitalCompleted / company.userCount) * 100) : 0;
                const englishRate = company.userCount > 0 ? Math.round((englishCompleted / company.userCount) * 100) : 0;

                adminCSV.push([
                    company.name,
                    company.userCount,
                    digitalCompleted,
                    englishCompleted,
                    digitalRate,
                    englishRate
                ]);
            });
        }

        downloadCSV(adminCSV, `platform-analytics-${timestamp}.csv`);
    }

    function exportComprehensiveJSON() {
        const timestamp = new Date().toISOString().split('T')[0];

        const exportData = {
            exportDate: new Date().toISOString(),
            summary: {
                totalAdmins: dashboardData.admins.length,
                totalCompanies: dashboardData.companies.length,
                totalUsers: dashboardData.companies.reduce((sum, c) => sum + c.userCount, 0),
                totalAssessments: dashboardData.assessments ? dashboardData.assessments.completedAssessments : 0
            },
            admins: dashboardData.admins.map(admin => ({
                id: admin.id,
                name: `${admin.firstname} ${admin.lastname}`,
                email: admin.email,
                company: admin.company,
                credits: admin.credits,
                subscriptionType: admin.subscriptionType,
                subscriptionActive: admin.subscriptionActive,
                trialDaysRemaining: admin.trialDaysRemaining,
                leadSource: admin.leadSource,
                createdAt: admin.createdAt,
                paid: admin.paid,
                referralStats: admin.referralStats
            })),
            companies: dashboardData.companies.map(company => ({
                id: company.id,
                name: company.name,
                adminEmail: company.adminEmail,
                userCount: company.userCount,
                createdAt: company.createdAt,
                users: company.users
            })),
            assessments: dashboardData.assessments
        };

        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `platform-data-${timestamp}.json`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    function generateAnalyticsReport() {
        const timestamp = new Date().toISOString().split('T')[0];

        // Calculate key metrics
        const totalAdmins = dashboardData.admins.length;
        const totalCompanies = dashboardData.companies.length;
        const totalUsers = dashboardData.companies.reduce((sum, c) => sum + c.userCount, 0);
        const totalCredits = dashboardData.admins.reduce((sum, a) => sum + (a.credits || 0), 0);

        const freeTrialAdmins = dashboardData.admins.filter(a => a.subscriptionType === 'freeTrial' && a.subscriptionActive).length;
        const paidAdmins = dashboardData.admins.filter(a => a.paid).length;
        const expiredTrials = dashboardData.admins.filter(a => a.isTrialExpired).length;

        const activeCompanies = dashboardData.companies.filter(c => c.userCount > 0).length;
        const avgUsersPerCompany = activeCompanies > 0 ? Math.round(totalUsers / activeCompanies) : 0;

        // Lead source breakdown
        const leadSources = {};
        dashboardData.admins.forEach(admin => {
            const source = admin.leadSource || 'Direct';
            leadSources[source] = (leadSources[source] || 0) + 1;
        });

        const reportHTML = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Platform Analytics Report - ${timestamp}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
                    .header { text-align: center; margin-bottom: 40px; }
                    .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
                    .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
                    .metric-value { font-size: 2em; font-weight: bold; color: #1547bb; }
                    .metric-label { color: #666; margin-top: 5px; }
                    .section { margin: 40px 0; }
                    .section h2 { color: #1547bb; border-bottom: 2px solid #1547bb; padding-bottom: 10px; }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
                    th { background-color: #f8f9fa; font-weight: bold; }
                    .footer { margin-top: 60px; text-align: center; color: #666; font-size: 0.9em; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Platform Analytics Report</h1>
                    <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
                </div>

                <div class="section">
                    <h2>Executive Summary</h2>
                    <div class="metric-grid">
                        <div class="metric-card">
                            <div class="metric-value">${totalAdmins}</div>
                            <div class="metric-label">Total Admin Accounts</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${totalCompanies}</div>
                            <div class="metric-label">Total Companies</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${totalUsers}</div>
                            <div class="metric-label">Total Platform Users</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${totalCredits.toLocaleString()}</div>
                            <div class="metric-label">Total Credits Distributed</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>Subscription Breakdown</h2>
                    <table>
                        <tr><th>Subscription Type</th><th>Count</th><th>Percentage</th></tr>
                        <tr><td>Free Trial</td><td>${freeTrialAdmins}</td><td>${Math.round((freeTrialAdmins/totalAdmins)*100)}%</td></tr>
                        <tr><td>Paid</td><td>${paidAdmins}</td><td>${Math.round((paidAdmins/totalAdmins)*100)}%</td></tr>
                        <tr><td>Expired Trial</td><td>${expiredTrials}</td><td>${Math.round((expiredTrials/totalAdmins)*100)}%</td></tr>
                    </table>
                </div>

                <div class="section">
                    <h2>Lead Source Analysis</h2>
                    <table>
                        <tr><th>Lead Source</th><th>Count</th><th>Percentage</th></tr>
                        ${Object.entries(leadSources).map(([source, count]) =>
                            `<tr><td>${source}</td><td>${count}</td><td>${Math.round((count/totalAdmins)*100)}%</td></tr>`
                        ).join('')}
                    </table>
                </div>

                <div class="section">
                    <h2>Company Analytics</h2>
                    <p><strong>Active Companies:</strong> ${activeCompanies} out of ${totalCompanies}</p>
                    <p><strong>Average Users per Company:</strong> ${avgUsersPerCompany}</p>
                    <p><strong>Companies with 10+ Users:</strong> ${dashboardData.companies.filter(c => c.userCount >= 10).length}</p>
                </div>

                <div class="footer">
                    <p>This report was generated automatically by the Skills Assess Super Admin Dashboard</p>
                    <p>For questions or support, contact the development team</p>
                </div>
            </body>
            </html>
        `;

        const blob = new Blob([reportHTML], { type: 'text/html' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `platform-analytics-report-${timestamp}.html`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    function loadSecuritySection() {
        console.log('Loading security section...');

        const securitySection = document.getElementById('security-section');
        const securityContent = securitySection.querySelector('.security-content');

        // Create security audit content
        const securityAudit = createSecurityAuditContent();
        securityContent.innerHTML = securityAudit;

        // Setup security event listeners
        setTimeout(() => {
            setupSecurityEventListeners();
        }, 100);
    }

    function createSecurityAuditContent() {
        const accessLogs = JSON.parse(localStorage.getItem('superAdminAccessLogs') || '[]');
        const activityLogs = JSON.parse(localStorage.getItem('superAdminActivityLogs') || '[]');

        // Combine and sort logs by timestamp
        const allLogs = [
            ...accessLogs.map(log => ({ ...log, type: 'access' })),
            ...activityLogs.map(log => ({ ...log, type: 'activity' }))
        ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        return `
            <div class="security-audit">
                <div class="security-header">
                    <h3>Security Audit Dashboard</h3>
                    <div class="security-controls">
                        <select id="log-filter">
                            <option value="all">All Logs</option>
                            <option value="access">Access Logs</option>
                            <option value="activity">Activity Logs</option>
                            <option value="failed">Failed Attempts</option>
                        </select>
                        <button id="clear-logs" class="clear-btn">Clear Logs</button>
                        <button id="export-logs" class="export-btn">Export Logs</button>
                    </div>
                </div>

                <!-- Security Summary -->
                <div class="security-summary">
                    <div class="summary-card">
                        <div class="summary-value">${accessLogs.length}</div>
                        <div class="summary-label">Total Access Attempts</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-value">${accessLogs.filter(log => log.success).length}</div>
                        <div class="summary-label">Successful Logins</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-value">${accessLogs.filter(log => !log.success).length}</div>
                        <div class="summary-label">Failed Attempts</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-value">${activityLogs.length}</div>
                        <div class="summary-label">Activity Events</div>
                    </div>
                </div>

                <!-- Audit Log Table -->
                <div class="audit-log-container">
                    <div class="log-table-wrapper">
                        <table class="audit-table">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Type</th>
                                    <th>Action</th>
                                    <th>Details</th>
                                    <th>Status</th>
                                    <th>Session ID</th>
                                </tr>
                            </thead>
                            <tbody id="audit-log-body">
                                ${allLogs.slice(0, 100).map(log => createLogRow(log)).join('')}
                            </tbody>
                        </table>
                    </div>
                    <div class="log-footer">
                        <div class="log-info">
                            Showing ${Math.min(allLogs.length, 100)} of ${allLogs.length} log entries
                        </div>
                    </div>
                </div>

                <!-- Security Recommendations -->
                <div class="security-recommendations">
                    <h4>Security Recommendations</h4>
                    <div class="recommendations-list">
                        <div class="recommendation-item">
                            <div class="recommendation-icon">⚠️</div>
                            <div class="recommendation-text">
                                <strong>Regular Monitoring:</strong> Review audit logs regularly for suspicious activity
                            </div>
                        </div>
                        <div class="recommendation-item">
                            <div class="recommendation-icon">🔒</div>
                            <div class="recommendation-text">
                                <strong>Session Management:</strong> Sessions automatically expire after 8 hours of inactivity
                            </div>
                        </div>
                        <div class="recommendation-item">
                            <div class="recommendation-icon">📊</div>
                            <div class="recommendation-text">
                                <strong>Data Access:</strong> All data access and exports are logged for compliance
                            </div>
                        </div>
                        <div class="recommendation-item">
                            <div class="recommendation-icon">🚨</div>
                            <div class="recommendation-text">
                                <strong>Failed Attempts:</strong> Multiple failed login attempts should be investigated
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function createLogRow(log) {
        const timestamp = new Date(log.timestamp).toLocaleString();
        const type = log.type === 'access' ? 'Access' : 'Activity';

        let action, details, status;

        if (log.type === 'access') {
            action = log.attemptType || 'login';
            details = log.email || 'N/A';
            status = log.success ?
                '<span class="status-success">Success</span>' :
                '<span class="status-failed">Failed</span>';
        } else {
            action = log.action || 'unknown';
            details = JSON.stringify(log.details || {}).substring(0, 50) + '...';
            status = '<span class="status-info">Info</span>';
        }

        return `
            <tr class="log-row" data-type="${log.type}" data-success="${log.success || 'true'}">
                <td class="timestamp">${timestamp}</td>
                <td class="log-type">${type}</td>
                <td class="action">${action}</td>
                <td class="details" title="${JSON.stringify(log.details || {})}">${details}</td>
                <td class="status">${status}</td>
                <td class="session-id">${log.sessionId || 'N/A'}</td>
            </tr>
        `;
    }

    function setupSecurityEventListeners() {
        // Log filter
        const logFilter = document.getElementById('log-filter');
        if (logFilter) {
            logFilter.addEventListener('change', (e) => {
                filterAuditLogs(e.target.value);
            });
        }

        // Clear logs button
        const clearBtn = document.getElementById('clear-logs');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to clear all audit logs? This action cannot be undone.')) {
                    clearAuditLogs();
                }
            });
        }

        // Export logs button
        const exportBtn = document.getElementById('export-logs');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                exportAuditLogs();
            });
        }
    }

    function filterAuditLogs(filterType) {
        const rows = document.querySelectorAll('.log-row');
        let visibleCount = 0;

        rows.forEach(row => {
            let shouldShow = true;

            switch (filterType) {
                case 'access':
                    shouldShow = row.dataset.type === 'access';
                    break;
                case 'activity':
                    shouldShow = row.dataset.type === 'activity';
                    break;
                case 'failed':
                    shouldShow = row.dataset.success === 'false';
                    break;
                case 'all':
                default:
                    shouldShow = true;
                    break;
            }

            row.style.display = shouldShow ? '' : 'none';
            if (shouldShow) visibleCount++;
        });

        // Update footer
        const logInfo = document.querySelector('.log-info');
        if (logInfo) {
            logInfo.textContent = `Showing ${visibleCount} filtered log entries`;
        }
    }

    function clearAuditLogs() {
        localStorage.removeItem('superAdminAccessLogs');
        localStorage.removeItem('superAdminActivityLogs');

        // Log the clearing action
        logActivity('audit_logs_cleared', { clearedBy: 'super_admin' });

        // Reload the security section
        loadSecuritySection();
    }

    function exportAuditLogs() {
        const accessLogs = JSON.parse(localStorage.getItem('superAdminAccessLogs') || '[]');
        const activityLogs = JSON.parse(localStorage.getItem('superAdminActivityLogs') || '[]');

        const csvData = [];
        csvData.push(['Timestamp', 'Type', 'Action', 'Details', 'Success', 'Session ID', 'User Agent']);

        // Add access logs
        accessLogs.forEach(log => {
            csvData.push([
                log.timestamp,
                'Access',
                log.attemptType || 'login',
                log.email || 'N/A',
                log.success ? 'Yes' : 'No',
                log.sessionId || 'N/A',
                log.userAgent || 'N/A'
            ]);
        });

        // Add activity logs
        activityLogs.forEach(log => {
            csvData.push([
                log.timestamp,
                'Activity',
                log.action,
                JSON.stringify(log.details || {}),
                'N/A',
                log.sessionId || 'N/A',
                log.userAgent || 'N/A'
            ]);
        });

        const timestamp = new Date().toISOString().split('T')[0];
        downloadCSV(csvData, `security-audit-logs-${timestamp}.csv`);

        // Log the export
        logActivity('audit_logs_exported', { exportedBy: 'super_admin', recordCount: csvData.length - 1 });
    }

    // Lazy loading functions for each section
    async function loadAdminSectionData() {
        if (!dashboardData.admins) {
            dashboardData.admins = await loadAdminData(false);
        }
        return { admins: dashboardData.admins };
    }

    async function loadCompanySectionData() {
        if (!dashboardData.companies) {
            dashboardData.companies = await loadCompanyData(false);
        }
        return { companies: dashboardData.companies };
    }

    async function loadUserSectionData() {
        if (!dashboardData.companies) {
            dashboardData.companies = await loadCompanyData(false);
        }
        if (!dashboardData.users) {
            dashboardData.users = await loadUserData(false);
        }
        return { users: dashboardData.users, companies: dashboardData.companies };
    }

    async function loadAssessmentSectionData() {
        if (!dashboardData.companies) {
            dashboardData.companies = await loadCompanyData(false);
        }
        if (!dashboardData.assessments) {
            dashboardData.assessments = await loadAssessmentData(false);
        }
        return { assessments: dashboardData.assessments, companies: dashboardData.companies };
    }

    async function loadAnalyticsSectionData() {
        // Load all data for advanced analytics
        if (!dashboardData.admins) {
            dashboardData.admins = await loadAdminData(false);
        }
        if (!dashboardData.companies) {
            dashboardData.companies = await loadCompanyData(false);
        }
        return {
            admins: dashboardData.admins,
            companies: dashboardData.companies,
            users: dashboardData.users,
            assessments: dashboardData.assessments
        };
    }

    async function loadSecuritySectionData() {
        // Security data is stored locally
        return {
            accessLogs: JSON.parse(localStorage.getItem('superAdminAccessLogs') || '[]'),
            activityLogs: JSON.parse(localStorage.getItem('superAdminActivityLogs') || '[]')
        };
    }

    // Render functions for each section
    function renderAdminSection(data) {
        const adminSection = document.getElementById('admins-section');
        const sectionContent = adminSection.querySelector('.section-content');

        if (!sectionContent) {
            adminSection.innerHTML = '<div class="section-content"></div>';
        }

        const adminTable = createAdminTable(data.admins);
        adminSection.querySelector('.section-content').innerHTML = adminTable;

        setTimeout(() => {
            setupAdminTableEventListeners();
        }, 100);
    }

    function renderCompanySection(data) {
        const companySection = document.getElementById('companies-section');
        const sectionContent = companySection.querySelector('.section-content');

        if (!sectionContent) {
            companySection.innerHTML = '<div class="section-content"></div>';
        }

        const companyTable = createCompanyTable(data.companies);
        companySection.querySelector('.section-content').innerHTML = companyTable;

        setTimeout(() => {
            setupCompanyTableEventListeners();
        }, 100);
    }

    function renderUserSection(data) {
        const userSection = document.getElementById('users-section');
        const sectionContent = userSection.querySelector('.section-content');

        if (!sectionContent) {
            userSection.innerHTML = '<div class="section-content"></div>';
        }

        const userTable = createUserTable(data.users, data.companies);
        userSection.querySelector('.section-content').innerHTML = userTable;

        setTimeout(() => {
            setupUserTableEventListeners();
        }, 100);
    }

    function renderAssessmentSection(data) {
        const assessmentSection = document.getElementById('assessments-section');
        const sectionContent = assessmentSection.querySelector('.section-content');

        if (!sectionContent) {
            assessmentSection.innerHTML = '<div class="section-content"></div>';
        }

        const assessmentContent = createAssessmentAnalytics(data.assessments, data.companies);
        assessmentSection.querySelector('.section-content').innerHTML = assessmentContent;

        setTimeout(() => {
            setupAssessmentEventListeners();
            initializeAssessmentCharts();
        }, 100);
    }

    function renderAnalyticsSection(data) {
        const analyticsSection = document.getElementById('analytics-section');
        const sectionContent = analyticsSection.querySelector('.section-content');

        if (!sectionContent) {
            analyticsSection.innerHTML = '<div class="section-content"></div>';
        }

        const advancedAnalytics = createAdvancedAnalytics();
        analyticsSection.querySelector('.section-content').innerHTML = advancedAnalytics;

        setTimeout(() => {
            initializeAdvancedCharts();
        }, 100);
    }

    function renderSecuritySection(data) {
        const securitySection = document.getElementById('security-section');
        const sectionContent = securitySection.querySelector('.section-content');

        if (!sectionContent) {
            securitySection.innerHTML = '<div class="section-content"></div>';
        }

        const securityAudit = createSecurityAuditContent();
        securitySection.querySelector('.section-content').innerHTML = securityAudit;

        setTimeout(() => {
            setupSecurityEventListeners();
        }, 100);
    }

    // Debug functions for testing (accessible from browser console)
    window.debugDashboard = {
        getDashboardData: () => dashboardData,
        getCache: () => sectionCache,
        reloadOverview: () => {
            sectionCache.delete('overview');
            loadOverviewData();
        },
        updateMetrics: () => updateOverviewMetrics(),
        initCharts: () => initializeCharts(),
        clearCache: () => sectionCache.clear()
    };

})();
